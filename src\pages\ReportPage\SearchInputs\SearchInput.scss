.search-input {
  width: 100%;
  height: 5rem;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  align-items: center;
  padding: 1.25rem 2.25rem;
  .inputs {
    height: 3rem;
  }
  .input-btn {
    height: 2rem;
    border: none;
    outline: none;
    background-color: var(--color-main);
    color: var(--color-white);
    &:hover {
      background-color: var(--color-main) !important;
      color: var(--color-white) !important;
    }
  }
  .excel-btn {
    outline: none;
    &:hover {
      color: black !important;
    }
  }
  .disabled-excel {
    background-color: maroon;
    color: #fff;
  }
  .top-switch {
    background-color: var(--color-main);
    &:hover {
      background-color: var(--color-main) !important;
    }
  }
}
