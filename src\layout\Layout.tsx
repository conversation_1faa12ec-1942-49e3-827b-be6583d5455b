import { ConfigProvider, Layout } from "antd";
import "./LayoutStyle.scss";
import CustomHeader from "./Header/Header";
import Main from "./Main/Main";
import CustomFooter from "./Footer/Footer";
import { FC, ReactNode, useEffect } from "react";
import dayjs from "dayjs";
import azAZ from "antd/es/locale/az_AZ";
import { useActiveTabKeyStore } from "@/store/activeTabkey";
dayjs.locale("az");
const CustomLayout: FC<{ children: ReactNode }> = ({ children }) => {
  const { setActiveKey } = useActiveTabKeyStore();
  const isReport = window.location.href.includes("report");
  useEffect(() => {
    if (isReport) {
      setActiveKey("2");
    } else {
      setActiveKey("1");
    }
  }, [isReport, setActiveKey]);
  return (
    <ConfigProvider locale={azAZ}>
      <Layout className='main-layout'>
        <CustomHeader />
        <Main children={children} />
        <CustomFooter />
      </Layout>
    </ConfigProvider>
  );
};

export default CustomLayout;
