import { QueryClientProvider } from "@tanstack/react-query";
import { ConfigProvider } from "antd";
import { Toaster } from "react-hot-toast";
import "./App.scss";
import FixedBackground from "./components/FixedBackground/FixedBackground";
import { queryClient } from "./config/query-client";
import { theme } from "./config/theme-config";
import MainRoutes from "./routes/MainRoutes";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider theme={theme}>
        <Toaster position='top-center' />
        <FixedBackground />
        <MainRoutes />
      </ConfigProvider>
      <ReactQueryDevtools initialIsOpen={false} buttonPosition='bottom-right' />
    </QueryClientProvider>
  );
}
