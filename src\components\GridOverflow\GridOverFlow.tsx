import { Tooltip } from "antd";
import "./GridOverFlow.scss";

type TCalssName =
  | "truncate-text"
  | "truncate-text-100px"
  | "truncate-text-200px"
  | "truncate-text-300px";

const GridOverFlow = ({ text, className }: { text: string; className?: TCalssName }) => {
  return (
    <Tooltip title={text}>
      <span className={className ?? "truncate-text"}>{text}</span>
    </Tooltip>
  );
};

export default GridOverFlow;
