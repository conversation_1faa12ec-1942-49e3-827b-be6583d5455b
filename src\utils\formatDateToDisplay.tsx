import { Dayjs } from "dayjs";

/**
 * Utility function to format date values for display
 * @param {Date|string|null|undefined} dateValue - The date value to format
 * @returns {string} Formatted date string or empty string if null/undefined
 */
export function formatDateForDisplay(dateValue: string | Dayjs) {
  // Handle null or undefined values
  if (dateValue === null || dateValue === undefined) {
    return "";
  }

  // Handle string values - return them as-is
  if (typeof dateValue === "string") {
    return dateValue;
  }

  // Handle Date objects - format them as needed
  if (dateValue instanceof Date) {
    // Example format: "YYYY-MM-DD" - adjust as needed
    const year = dateValue.getFullYear();
    const month = String(dateValue.getMonth() + 1).padStart(2, "0");
    const day = String(dateValue.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  }

  // For any other type, return empty string
  return "";
}
