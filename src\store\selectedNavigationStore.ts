import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

type SelectedKeysSlice = {
  selectedKey: string;
  setSelectedKeys: (key: string) => void;
};

const createSelectedKeysSlice: StateCreator<SelectedKeysSlice, [["zustand/devtools", never]]> = (
  set,
) => ({
  selectedKey: "",
  setSelectedKeys: (keys) => set({ selectedKey: keys }, false, `setSelectedKey ${keys}`),
});

export const useSelectedKeysStore = create<SelectedKeysSlice>()(devtools(createSelectedKeysSlice));
