import { useNotificationPopStore } from "@/store/notificationStore";
import { RefObject, useEffect } from "react";

export const useGlobalEvents = (element: RefObject<HTMLElement>) => {
  const { setNotificationState } = useNotificationPopStore();
  useEffect(() => {
    const handleWindowClick = (e: MouseEvent) => {
      if (element.current && !element.current.contains(e.target as Node)) {
        setNotificationState(false);
      } else if (e.target instanceof SVGElement && e.target.getAttribute("data-icon") === "bell") {
        setNotificationState(true);
      }
    };

    window.addEventListener("click", handleWindowClick);

    return () => {
      window.removeEventListener("click", handleWindowClick);
    };
  }, [setNotificationState, element]);
  return {};
};
