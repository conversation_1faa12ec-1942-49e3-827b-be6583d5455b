import React, { ChangeEvent } from "react";
import "./CustomMaskInput.scss";
import { usePhoneNumberStore } from "@/store/phoneNumberStore";
import { formatValue } from "@/utils";

const PhoneInputCustom: React.FC = () => {
  const { phoneNumber, setPhoneNumber } = usePhoneNumberStore();

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;
    const formatted = formatValue(input);
    setPhoneNumber(formatted);
  };

  return (
    <div className={`input-container focused`}>
      <label className='input-label' htmlFor='tel'>
        Telefon
      </label>
      <input
        name='tel'
        className='input-wrap'
        type='tel'
        value={phoneNumber}
        onChange={handleChange}
        placeholder='+994 xx xxx   xx   xx'
      />
    </div>
  );
};

export default PhoneInputCustom;
