import { QueryKeys } from "@/enums/queryKeyEnums";
import { Visit } from "@/services/visit-controller/vistiController";
import { SearchParmasType } from "@/types";
import { useQuery } from "@tanstack/react-query";

const useReportFormats = ({
  startDate,
  endDate,
  enabled,
}: {
  startDate: SearchParmasType;
  endDate: SearchParmasType;
  enabled: boolean;
}) => {
  return useQuery({
    queryKey: [QueryKeys.REPORT_FORMATS],
    queryFn: () => Visit.getReportFormats(startDate, endDate),
    enabled,
  });
};
export default useReportFormats;
