import { QueryKeys } from "@/enums/queryKeyEnums";
import { NotificationController } from "@/services/notification-controller/notification";

import { useQuery } from "@tanstack/react-query";

const useNotificationsCountQuery = () => {
  return useQuery({
    queryKey: [QueryKeys.NOTIFICATIONS_COUNT],
    queryFn: NotificationController.getNotificationCount,
  });
};
export default useNotificationsCountQuery;
