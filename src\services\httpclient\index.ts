import axios, { AxiosInstance, AxiosResponse, AxiosError } from "axios";
import toast from "react-hot-toast";
const baseUrl = import.meta.env.VITE_APP_SERVER_URL;

export const httpClient: AxiosInstance = axios.create({
  baseURL: baseUrl,
  withCredentials: true,
});

type ServerErrorData = { message: string };

const errorMessageByCode: Record<number, string> = {
  400: "400 Validasiya xətası",
  404: "404 Tapılmayib!",
  500: "500 Server xətası",
};

const setupInterceptors = () => {
  httpClient.interceptors.request.use(
    (requestConfig) => {
      return requestConfig;
    },
    (error: AxiosError) => {
      console.error("Sorğu xətası:", error);
      return Promise.reject(error);
    },
  );

  httpClient.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error: AxiosError) => {
      if (error.response) {
        const errorStatus = error.response.status;
        const errorData = error.response.data as ServerErrorData;

        if (errorStatus === 401 || errorStatus === 403) {
          toast.error(error.response.statusText);
          localStorage.clear();
          window.location.href = "/";
        }
        if (errorData.message) {
          toast.error(errorData.message);
        } else if (errorStatus in errorMessageByCode) {
          toast.error(errorMessageByCode[errorStatus]);
        } else {
          toast.error(`Server status kodu ${errorStatus}`);
        }
      } else if (error.request) {
        if (error.code === "ECONNABORTED") {
          toast.error("Sorğu üçün vaxt yekunlaşdı yenidən yoxlayın.");
        } else {
          toast.error("İnternet bağlantısını yoxlayın.");
        }
      } else {
        toast.error(`Gözlənilməz xəta: ${error.message}`);
      }
      return Promise.reject(error);
    },
  );
};

setupInterceptors();
