import { useVisitorStore } from "@/store/visitorsStore";
import "./ConfirmationBlock.scss";
import ConfirmButton from "./ConfirmButton/ConfirmButton";
import { useServicesStore } from "@/store/servicesStore";
import ContactInput from "./ContactComponent/components/ContactInput";

const ConfirmationBlock = () => {
  const { visitors } = useVisitorStore();
  const { serviceIds } = useServicesStore();
  const sumOfServices = serviceIds.reduce((acc, element) => acc + element.sum, 0);
  const sumOfVisitors = visitors.reduce((acc, element) => acc + element.sum, 0);

  return (
    <div className='confirmation-block'>
      <div className='sum'>Cəmi: {(sumOfServices + sumOfVisitors).toFixed(2)} ₼</div>
      <ContactInput />
      <ConfirmButton />
    </div>
  );
};

export default ConfirmationBlock;
