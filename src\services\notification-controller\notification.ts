import { Endpoints } from "@/enums/endPointEnums";
import { TGetNotificationService, TResponse } from "@/types";
import { AxiosResponse } from "axios";
import { httpClient } from "../httpclient";

export class NotificationController {
  static async get(): Promise<TGetNotificationService> {
    const response: AxiosResponse<TGetNotificationService> = await httpClient.get(
      `${Endpoints.NOTIFICATION}`,
    );
    return response.data;
  }
  static async getNotificationCount(): Promise<number> {
    const response: AxiosResponse<number> = await httpClient.get(`${Endpoints.NOTIFICATION_COUNT}`);
    return response.data;
  }
  static async readById(id: string | number): Promise<TResponse> {
    const response: AxiosResponse<TResponse> = await httpClient.put(
      `${Endpoints.NOTIFICATION_READ}/${id}`,
    );
    return response.data;
  }
  static async readAll(): Promise<TResponse> {
    const response: AxiosResponse<TResponse> = await httpClient.put(
      `${Endpoints.NOTIFICATION_READ_ALL}`,
    );
    return response.data;
  }
}
