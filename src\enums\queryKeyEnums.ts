export enum QueryKeys {
  Navigations = "navigations",
  FILE_SVG = "svg-file",
  PARAMETERS = "parameters",
  SERVICES = "services",
  NOTIFICATIONS = "notifications",
  NOTIFICATIONS_COUNT = "notification-count",
  REPORT_CATEGORIES = "report-categories",
  REPORT_SERVICES = "report-services",
  REPORT_FORMATS = "report-formats",
  REPORT_CATEGORIES_STATISTIC = "report-categories-statistic",
  REPORT_SERVICES_STATISTIC = "report-services-statistic",
  REPORT_FORMATS_STATISTIC = "report-formats-statistic",
  REPORT_CATEGORIES_STATISTIC_TICKET = "report-categories-statistic-ticket",
  REPORT_SERVICES_STATISTIC_TICKET = "report-services-statistic-ticket",
  REPORT_FORMATS_STATISTIC_TICKET = "report-formats-statistic-ticket",
  REPORT_CATEGORIES_COUNT_MONEY = "report-categories-count-money",
  REPORT_FORMATS_COUNT_MONEY = "report-formats-count-money",
  REPORT_SERVICES_COUNT_MONEY = "report-services-count-money",
  REPORT_GRID = "report-grid",
  REPORT_GRID_BY_DATE = "report-grid-by-date",
  REPORT_RESERVED_TICKET = "report-reserved-ticket",
}
