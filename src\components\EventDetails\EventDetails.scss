.event-details {
  display: flex;
  height: 5.5rem;
  padding: 0.5rem 0.5rem;
  align-items: center;
  gap: 1.5rem;
  align-self: stretch;
  border-radius: 0.75rem;
  border: 1px solid var(--Greyscale-200);
  margin-bottom: 0.5rem;
  .event-input {
    .picker-wrapper-all {
      display: flex;
      width: 100%;
      justify-content: space-between;
      gap: 5px;
      .ant-picker .ant-picker-input > input {
        height: 1.7rem !important;
      }
    }
    p {
      color: var(--Greyscale-900,);
      font-size: 1rem;
      font-style: normal;
      font-weight: 400;
      line-height: 1.25rem;
    }
    &.name {
      flex: 3;
      .name-input {
        height: 2.3rem !important;
        &:focus {
          outline-color: var(--Secondary-1000);
        }
      }
    }
    &.contact {
      flex: 1;
    }
    &.end-date {
      flex: 1.2;
    }
    &.start-date {
      flex: 1.2;
    }
  }
}
