import { Endpoints } from "@/enums/endPointEnums";
import { AxiosResponse } from "axios";
import { NavigationResponseType } from "@/types";
import { httpClient } from "../httpclient";

export class VisitFormatService {
  static async getNavigations(): Promise<NavigationResponseType> {
    const response: AxiosResponse<NavigationResponseType> = await httpClient.get(
      `${Endpoints.VISIT_FORMAT}?page=0&size=100`,
    );
    return response.data;
  }
}
