import { Endpoints } from "@/enums/endPointEnums";
import { TrequestBodyPost, TResponseVisitCategory, TVisitCategory } from "@/types";
import { AxiosResponse } from "axios";
import { httpClient } from "../httpclient";

export class VisitCategory {
  static async get(id?: number | string): Promise<TVisitCategory> {
    const response: AxiosResponse<TVisitCategory> = await httpClient.get(
      `${Endpoints.VISIT_CATEGORY}?visitFormats=${id}`,
    );
    return response.data;
  }
  static async put(id: string | number): Promise<TResponseVisitCategory> {
    const response: AxiosResponse<TResponseVisitCategory> = await httpClient.put(
      `${Endpoints.VISIT_CATEGORY}/${id}`,
    );
    return response.data;
  }
  static async patch(id: string | number): Promise<TResponseVisitCategory> {
    const response: AxiosResponse<TResponseVisitCategory> = await httpClient.patch(
      `${Endpoints.VISIT_CATEGORY}/${id}`,
    );
    return response.data;
  }
  static async post(postData: TrequestBodyPost): Promise<TResponseVisitCategory> {
    const response: AxiosResponse<TResponseVisitCategory> = await httpClient.post(
      `${Endpoints.VISIT_CATEGORY}`,
      postData,
    );
    return response.data;
  }
}
