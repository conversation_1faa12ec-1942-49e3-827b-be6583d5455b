import { QueryKeys } from "@/enums/queryKeyEnums";
import { Visit } from "@/services/visit-controller/vistiController";
import { SearchParmasType } from "@/types";
import { useQuery } from "@tanstack/react-query";

const useReportCountMoneyFormats = ({
  startDate,
  endDate,
  enabled,
}: {
  startDate: SearchParmasType;
  endDate: SearchParmasType;
  enabled: boolean;
}) => {
  return useQuery({
    queryKey: [QueryKeys.REPORT_FORMATS_COUNT_MONEY],
    queryFn: () => Visit.getCountMoneyFormat(startDate, endDate),
    enabled,
  });
};
export default useReportCountMoneyFormats;
