import { useNavigate } from "react-router-dom";
import "./CustomNavbarButtonStyle.scss";
import { useSelectedKeysStore } from "@/store/selectedNavigationStore";
import { usePageTypeStore } from "@/store/pageTypeStore";
import { useVisitorStore } from "@/store/visitorsStore";
import { useServicesStore } from "@/store/servicesStore";
import { useEventStore } from "@/store/eventStore";
import { usePhoneNumberStore } from "@/store/phoneNumberStore";
import { Tooltip } from "antd";
import { useActiveTabKeyStore } from "@/store/activeTabkey";
import { FC } from "react";

const CustomNavbarButton: FC<CustomNavbarButtonType> = ({
  iconActive,
  iconDeactive,
  keyName,
  id,
  typePage,
}) => {
  const { selectedKey, setSelectedKeys } = useSelectedKeysStore();
  const { setPageType } = usePageTypeStore();
  const { clearVisitors } = useVisitorStore();
  const { clearServices } = useServicesStore();
  const { clearPhoneNumber } = usePhoneNumberStore();
  const { setActiveKey } = useActiveTabKeyStore();
  const { clear } = useEventStore();
  const isActive = selectedKey === keyName;
  const navigate = useNavigate();
  const classNames = `navbar-button ${isActive && "btn-active"}`;
  const handleClick = () => {
    setSelectedKeys(keyName);
    setPageType(typePage);
    navigate(id.toString());
    clearVisitors();
    clearServices();
    clear();
    clearPhoneNumber();
    setActiveKey("1");
  };

  return (
    <Tooltip title={keyName}>
      <div className='head-nav-item'>
        <button onClick={handleClick} className={classNames}>
          {<img alt='head-nav-item.svg' src={isActive ? iconActive : iconDeactive} />}
        </button>
      </div>
    </Tooltip>
  );
};

export default CustomNavbarButton;

type CustomNavbarButtonType = Readonly<{
  typePage: string;
  iconActive?: string;
  iconDeactive?: string;
  keyName: string;
  id: number | string;
}>;
