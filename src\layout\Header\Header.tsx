import ExitButton from "@/components/ExitButton/ExitButton";
import LogoIcon from "@/components/icons/LogoIcon";
import { DownOutlined, UserOutlined } from "@ant-design/icons";
import { Dropdown, MenuProps, Space } from "antd";
import { Header } from "antd/es/layout/layout";
import HeadTabulation from "./components/HeadTabulation/HeadTabulation";
import IconTitle from "./components/IconTitle/IconTitle";
import Navbar from "./components/Navbar/Navbar";
import { useAuthStore } from "@/store/authStore";
import Notification from "@/components/Notification/Notification";
const CustomHeader = () => {
  const { user } = useAuthStore();

  return (
    <Header className='header'>
      <div className='head-container'>
        <div className='head-top'>
          <div className='left'>
            <div className='logo'>
              <LogoIcon />
              <IconTitle />
            </div>
            <div className='tabulation'>
              <HeadTabulation />
            </div>
          </div>
          <div className='right'>
            <div className='notification'>
              <Notification user={user} />
            </div>
            <div className='profile'>
              <UserOutlined />
            </div>
            <Dropdown menu={{ items }} trigger={["click"]}>
              <button
                className='user-name-title'
                onClick={(e) => e.preventDefault()}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                  }
                }}
              >
                <Space className='dropdown-title'>
                  <p>{user.name}</p>
                  <DownOutlined />
                </Space>
              </button>
            </Dropdown>
          </div>
        </div>
        <div className='head-bottom-container'>
          <Navbar />
        </div>
      </div>
    </Header>
  );
};

export default CustomHeader;

const items: MenuProps["items"] = [
  {
    label: <ExitButton />,
    key: "3",
  },
];
