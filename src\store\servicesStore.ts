import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

type ServiceStoreSlice = {
  serviceIds: { id: number; sum: number }[];
  setServices: (key: { id: number; sum: number }) => void;
  clearServices: () => void;
  removeService: (key: number) => void;
};

const createServicesSlice: StateCreator<ServiceStoreSlice, [["zustand/devtools", never]]> = (
  set,
) => ({
  serviceIds: [],
  setServices: (key) =>
    set(
      (state: ServiceStoreSlice) => ({
        serviceIds: [...state.serviceIds, key],
      }),
      false,
      `servicesSetState ${key}`,
    ),
  clearServices: () =>
    set({ serviceIds: [] }, false, ` notificationState ${"clear 'clearservices'"}`),

  removeService: (key) =>
    set(
      (state: ServiceStoreSlice) => ({
        serviceIds: state.serviceIds.filter((item) => item.id !== key),
      }),
      false,
      `removeService ${key}`,
    ),
});

export const useServicesStore = create<ServiceStoreSlice>()(devtools(createServicesSlice));
