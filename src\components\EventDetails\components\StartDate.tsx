import { useEventStore } from "@/store/eventStore";
import { dateTyppe } from "@/types";
import { DatePicker, TimePicker } from "antd";
import { FC } from "react";
const StartDate: FC = () => {
  const { startDate, startTime, setStartDate, setStartTime } = useEventStore();

  const handleDateChange = (date: dateTyppe) => {
    setStartDate(date);
  };

  const handleTimeChange = (time: dateTyppe) => {
    setStartTime(time);
  };
  return (
    <div className='event-input start-date'>
      <p>Başlama tarixi</p>
      <div>
        <div className='picker-wrapper-all'>
          <DatePicker
            style={{ width: "100%" }}
            value={startDate}
            onChange={handleDateChange}
            placeholder='Gün.Ay.İl'
            format='DD.MM.YYYY'
          />
          <TimePicker
            style={{ width: "82%" }}
            value={startTime}
            onChange={handleTimeChange}
            format='HH:mm'
            placeholder='Saat:Dəqiqə'
          />
        </div>
      </div>
    </div>
  );
};

export default StartDate;
