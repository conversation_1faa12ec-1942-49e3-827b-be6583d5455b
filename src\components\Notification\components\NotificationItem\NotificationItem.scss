.notification-item {
  gap: 1rem;
  color: black !important;
  height: 3.25rem;
  display: flex;
  align-items: center;
  width: 100%;
  // background-color: red;
  .content-notification-item {
    display: flex;
    width: 83%;
    height: 100%;
    flex-direction: column;
    .title {
      color: maroon !important;
      font-size: 1rem;
      font-style: normal;
      font-weight: 400;
      line-height: 1.5rem;
    }
    .title-date {
      color: #3f5843;
      font-size: 0.8rem;
      font-style: normal;
      font-weight: 400;
      line-height: 1.5rem;
    }
    .description {
      overflow: hidden;
      color: var(--Greyscale-500, #718096);
      text-overflow: ellipsis;
      font-size: 0.875rem;
      font-style: normal;
      font-weight: 400;
      line-height: 1.5rem;
    }
  }
  .date {
    color: var(--Greyscale-500, #718096);
    font-size: 0.75rem;
    font-style: normal;
    font-weight: 600;
    line-height: 1rem;
  }
  .new-date {
    color: var(--Greyscale-500, #419522);
    font-size: 0.75rem;
    font-style: normal;
    font-weight: 600;
    line-height: 1rem;
  }
}
