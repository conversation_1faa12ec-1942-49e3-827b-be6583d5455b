import { Visit } from "@/services/visit-controller/vistiController";
import { useEventStore } from "@/store/eventStore";
import { usePhoneNumberStore } from "@/store/phoneNumberStore";
import { useServicesStore } from "@/store/servicesStore";
import { useTriggerStore } from "@/store/triggerStore";
import { useVisitorStore } from "@/store/visitorsStore";
import { TVisitPostRequestBody } from "@/types";
import { useMutation } from "@tanstack/react-query";

const useVisit = () => {
  const { setTrigger, trigger } = useTriggerStore();
  const { clearVisitors } = useVisitorStore();
  const { clearServices } = useServicesStore();
  const { clearPhoneNumber } = usePhoneNumberStore();
  const { clear } = useEventStore();
  return useMutation({
    mutationFn: (postData: TVisitPostRequestBody | null) => Visit.post(postData),
    onSuccess: (data) => {
      const blob = new Blob([data], { type: "application/pdf" });
      const fileURL = URL.createObjectURL(blob);
      clearVisitors();
      clearServices();
      clearPhoneNumber();
      setTrigger(!trigger);
      clear();
      const firstInput = document.querySelector("input");
      firstInput?.focus();
      window.open(fileURL, "_blank");
    },
  });
};

export default useVisit;
