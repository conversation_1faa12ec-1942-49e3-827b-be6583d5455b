import { PageType } from "@/enums/page-type";
import { useEventStore } from "@/store/eventStore";
import { usePageTypeStore } from "@/store/pageTypeStore";
import { usePhoneNumberStore } from "@/store/phoneNumberStore";
import { useServicesStore } from "@/store/servicesStore";
import { useVisitorStore } from "@/store/visitorsStore";
import { returnCombinedDateAsIsoString } from "@/utils";
import { formatPhoneNumberForWhatsApp } from "@/utils/formatPhoneNumberFOrWhatsApp";
import { useParams } from "react-router-dom";

const useConfirmationPayload = () => {
  const { pageType } = usePageTypeStore();
  const { id } = useParams();
  const { phoneNumber } = usePhoneNumberStore();
  const { endDate, endTime, startDate, startTime, eventName } = useEventStore();
  const { visitors } = useVisitorStore();
  const { serviceIds } = useServicesStore();
  if (!id) return { payload: null };

  const obj =
    pageType === PageType.INDIVIDUAL
      ? {
          visitFormat: +id,
          services: serviceIds.map((item) => item.id),
          mobilePhoneNumber: formatPhoneNumberForWhatsApp(phoneNumber),
          visitors: visitors.filter((visit) => visit.numberOfVisitors > 0),
        }
      : {
          visitFormat: +id,
          services: serviceIds.map((item) => item.id),
          event: {
            name: eventName,
            start: returnCombinedDateAsIsoString({ date: startDate, time: startTime }),
            end: returnCombinedDateAsIsoString({ date: endDate, time: endTime }),
          },
          mobilePhoneNumber: formatPhoneNumberForWhatsApp(phoneNumber),
          visitors: visitors.filter((visit) => visit.numberOfVisitors > 0),
        };
  return { payload: obj };
};

export default useConfirmationPayload;
