import React from "react";
import "./no-data.scss";

interface NoDataMessageProps {
  message?: string;
}

const NoDataMessage: React.FC<NoDataMessageProps> = ({
  message = "Məlumat tapılmadı. Axtarış parametrlərini dəyişməyi sınayın.",
}) => {
  return (
    <div className='no-data-message'>
      <div className='icon'>🌱</div>
      <p>{message}</p>
    </div>
  );
};

export default NoDataMessage;
