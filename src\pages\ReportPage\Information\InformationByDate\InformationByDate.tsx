import { usePaginationStore } from "@/store/paginationStore";
import "./informationByDate.scss";
import { useSearchParamsStore } from "@/store/searchParamsStore";
import useReportByDateGrid from "@/hooks/queryhooks/useReportByDateGrid";
import { REPORT } from "@/constants/report";
import { Loading } from "@/components/CustomLoading";
import NoDataMessage from "../../GeneralInformation/no-data";
import { Table } from "antd";
import { ColumnsType } from "antd/es/table";
import GridOverFlow from "@/components/GridOverflow/GridOverFlow";
import { formatNumber, formatThousand } from "@/utils/formatNumber";
import { TGetReportByDateContent } from "@/types";

const columns: ColumnsType<TGetReportByDateContent> = [
  {
    title: "Tarix",
    dataIndex: "date",
    key: "date",
    width: 70,
    align: "right",
    render: (text: string) => <GridOverFlow text={text.split("-").reverse().join(".")} />,
  },
  {
    title: "Biletinlərin sayı",
    dataIndex: "numberOfTickets",
    key: "numberOfTickets",
    width: 100,
    align: "right",
    render: (text: string) => <GridOverFlow text={formatThousand(text)} />,
  },
  {
    title: "Biletlərdən satış üzrə dövriyyə",
    dataIndex: "totalTicketPrice",
    key: "totalTicketPrice",
    width: 120,
    align: "right",
    render: (text: string) => <GridOverFlow text={`${formatNumber(text)} ₼`} />,
  },
  {
    title: "Ziyarətçi sayı",
    dataIndex: "numberOfVisitors",
    key: "numberOfVisitors",
    width: 100,
    align: "right",
    render: (text: string) => <GridOverFlow text={formatThousand(text)} />,
  },
  {
    title: "Əlavə xidmətlərin sayı",
    dataIndex: "numberOfServices",
    key: "numberOfServices",
    width: 100,
    align: "right",
    render: (text: string) => <GridOverFlow text={formatThousand(text)} />,
  },
  {
    title: "Əlavə xidmətlər üzrə dövriyyə",
    dataIndex: "totalServicePrice",
    key: "totalServicePrice",
    width: 120,
    align: "right",
    render: (text: string) => <GridOverFlow text={`${formatNumber(text)} ₼`} />,
  },
  {
    title: "Cəmi",
    dataIndex: "total",
    key: "total",
    width: 80,
    align: "right",
    render: (_: string, record: TGetReportByDateContent) => {
      return (
        <GridOverFlow
          text={`${formatNumber(
            (record.totalTicketPrice + record.totalServicePrice).toString(),
          )} ₼`}
        />
      );
    },
  },
];
const InformationByDate = () => {
  const { pageSize, setPageSize } = usePaginationStore();

  const { startDate, endDate, reportType } = useSearchParamsStore();

  const { data, isFetching } = useReportByDateGrid({
    startDate,
    endDate,
    page: pageSize.page - 1,
    size: pageSize.size,
    enabled: reportType === REPORT.by_date,
  });

  if (isFetching) {
    return <Loading />;
  }
  const paginationConfig = {
    current: pageSize.page,
    pageSize: pageSize.size,
    total: data?.totalElements ?? 0, // Ensure this is set to the total number of items
    showSizeChanger: true,
    pageSizeOptions: ["10", "20", "50", "100"],
    showTotal: (total: number, range: [number, number]) =>
      `${range[0]}-${range[1]} sətirlər  (cəmi: ${total} sətir)`,
    onChange: (page: number, size: number) => {
      setPageSize({
        page,
        size,
      });
    },
  };
  // return (
  //   <div className='information-by-date'>
  //     <div className='loading-container'>
  //       <img className='loading-gif' src={loading_gif} alt='loading_construction.gif' />
  //       <p>Səhifə tərtibat edilir...</p>
  //     </div>
  //   </div>
  // );
  return (
    <div className='table-wrapper'>
      {data?.content && data?.content?.length > 0 ? (
        <Table
          className='custom-table'
          columns={columns}
          dataSource={data.content}
          pagination={paginationConfig}
          scroll={{
            y: "350px",
            x: "max-content",
          }}
        />
      ) : (
        <NoDataMessage />
      )}
    </div>
  );
};

export default InformationByDate;
