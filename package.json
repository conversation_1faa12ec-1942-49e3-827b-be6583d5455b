{"name": "@zkteco", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:prod": "vite --mode production", "build:dev": "tsc -b && vite build --mode development", "build:prod": "tsc -b && vite build --mode production", "lint": "eslint .", "preview": "vite preview", "prepare": "husky", "format": "prettier --write src"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@tanstack/react-query": "^5.61.5", "@tanstack/react-query-devtools": "^5.60.6", "antd": "^5.22.2", "apexcharts": "^4.4.0", "axios": "^1.7.8", "dayjs": "^1.11.13", "framer-motion": "^12.0.6", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-router-dom": "^7.1.1", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "figlet": "^1.8.0", "globals": "^15.12.0", "husky": "^9.1.7", "prettier": "^3.4.1", "sass": "^1.81.0", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}}