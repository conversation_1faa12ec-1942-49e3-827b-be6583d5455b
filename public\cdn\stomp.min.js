(function(){var t,n,e,i,r={}.hasOwnProperty,o=[].slice;t={LF:"\n",NULL:"\0"},e=function(){var n;function e(t,n,e){this.command=t,this.headers=null!=n?n:{},this.body=null!=e?e:""}return e.prototype.toString=function(){var n,i,o,s,u;for(i in n=[this.command],(o=!1===this.headers["content-length"])&&delete this.headers["content-length"],u=this.headers)r.call(u,i)&&(s=u[i],n.push(""+i+":"+s));return this.body&&!o&&n.push("content-length:"+e.sizeOfUTF8(this.body)),n.push(t.LF+this.body),n.join(t.LF)},e.sizeOfUTF8=function(t){return t?encodeURI(t).match(/%..|./g).length:0},n=function(n){var i,r,o,s,u,c,a,h,f,l,p,d,b,g,v,m,y;for(b=0,s=n.search(RegExp(""+t.LF+t.LF)),o=(u=n.substring(0,s).split(t.LF)).shift(),c={},d=function(t){return t.replace(/^\s+|\s+$/g,"")},v=(m=u.reverse()).length;b<v;b++)h=(l=m[b]).indexOf(":"),c[d(l.substring(0,h))]=d(l.substring(h+1));if(i="",p=s+2,c["content-length"])f=parseInt(c["content-length"]),i=(""+n).substring(p,p+f);else for(r=null,a=g=p,y=n.length;(p<=y?g<y:g>y)&&(r=n.charAt(a))!==t.NULL;a=p<=y?++g:--g)i+=r;return new e(o,c,i)},e.unmarshall=function(e){var i;return function(){var r,o,s,u;for(r=0,s=e.split(RegExp(""+t.NULL+t.LF+"*")),u=[],o=s.length;r<o;r++)(null!=(i=s[r])?i.length:void 0)>0&&u.push(n(i));return u}()},e.marshall=function(n,i,r){var o;return(o=new e(n,i,r)).toString()+t.NULL},e}(),n=function(){var n;function r(t){this.ws=t,this.ws.binaryType="arraybuffer",this.counter=0,this.connected=!1,this.heartbeat={outgoing:1e4,incoming:1e4},this.maxWebSocketFrameSize=16384,this.subscriptions={}}return r.prototype.debug=function(t){var n;return"undefined"!=typeof window&&null!==window&&null!=(n=window.console)?n.log(t):void 0},n=function(){return Date.now?Date.now():new Date().valueOf},r.prototype._transmit=function(t,n,i){var r;for(r=e.marshall(t,n,i),"function"==typeof this.debug&&this.debug(">>> "+r);;){if(!(r.length>this.maxWebSocketFrameSize))return this.ws.send(r);this.ws.send(r.substring(0,this.maxWebSocketFrameSize)),r=r.substring(this.maxWebSocketFrameSize),"function"==typeof this.debug&&this.debug("remaining = "+r.length)}},r.prototype._setupHeartbeat=function(e){var r,o,s,u,c,a,h,f;if(((c=e.version)===i.VERSIONS.V1_1||c===i.VERSIONS.V1_2)&&(o=(a=function(){var t,n,i,r;for(t=0,i=e["heart-beat"].split(","),r=[],n=i.length;t<n;t++)u=i[t],r.push(parseInt(u));return r}())[0],r=a[1],!(0===this.heartbeat.outgoing||0===r)&&(s=Math.max(this.heartbeat.outgoing,r),"function"==typeof this.debug&&this.debug("send PING every "+s+"ms"),this.pinger=i.setInterval(s,(h=this,function(){return h.ws.send(t.LF),"function"==typeof h.debug?h.debug(">>> PING"):void 0}))),!(0===this.heartbeat.incoming||0===o)))return s=Math.max(this.heartbeat.incoming,o),"function"==typeof this.debug&&this.debug("check PONG every "+s+"ms"),this.ponger=i.setInterval(s,(f=this,function(){var t;if((t=n()-f.serverActivity)>2*s)return"function"==typeof f.debug&&f.debug("did not receive server activity for the last "+t+"ms"),f.ws.close()}))},r.prototype._parseConnect=function(){var t,n,e,i;switch(t=1<=arguments.length?o.call(arguments,0):[],i={},t.length){case 2:i=t[0],n=t[1];break;case 3:t[1]instanceof Function?(i=t[0],n=t[1],e=t[2]):(i.login=t[0],i.passcode=t[1],n=t[2]);break;case 4:i.login=t[0],i.passcode=t[1],n=t[2],e=t[3];break;default:i.login=t[0],i.passcode=t[1],n=t[2],e=t[3],i.host=t[4]}return[i,n,e]},r.prototype.connect=function(){var r,s,u,c,a,h,f;return r=1<=arguments.length?o.call(arguments,0):[],u=(c=this._parseConnect.apply(this,r))[0],this.connectCallback=c[1],s=c[2],"function"==typeof this.debug&&this.debug("Opening Web Socket..."),this.ws.onmessage=(a=this,function(i){var r,o,u,c,h,f,l,p,d,b,g,v;if(c="undefined"!=typeof ArrayBuffer&&i.data instanceof ArrayBuffer?(r=new Uint8Array(i.data),"function"==typeof a.debug&&a.debug("--- got data length: "+r.length),(function(){var t,n,e;for(t=0,e=[],n=r.length;t<n;t++)o=r[t],e.push(String.fromCharCode(o));return e})().join("")):i.data,a.serverActivity=n(),c===t.LF){"function"==typeof a.debug&&a.debug("<<< PONG");return}for("function"==typeof a.debug&&a.debug("<<< "+c),g=e.unmarshall(c),v=[],d=0,b=g.length;d<b;d++)switch((h=g[d]).command){case"CONNECTED":"function"==typeof a.debug&&a.debug("connected to server "+h.headers.server),a.connected=!0,a._setupHeartbeat(h.headers),v.push("function"==typeof a.connectCallback?a.connectCallback(h):void 0);break;case"MESSAGE":p=h.headers.subscription,(l=a.subscriptions[p]||a.onreceive)?(u=a,f=h.headers["message-id"],h.ack=function(t){return null==t&&(t={}),u.ack(f,p,t)},h.nack=function(t){return null==t&&(t={}),u.nack(f,p,t)},v.push(l(h))):v.push("function"==typeof a.debug?a.debug("Unhandled received MESSAGE: "+h):void 0);break;case"RECEIPT":v.push("function"==typeof a.onreceipt?a.onreceipt(h):void 0);break;case"ERROR":v.push("function"==typeof s?s(h):void 0);break;default:v.push("function"==typeof a.debug?a.debug("Unhandled frame: "+h):void 0)}return v}),this.ws.onclose=(h=this,function(){var t;return t="Whoops! Lost connection to "+h.ws.url,"function"==typeof h.debug&&h.debug(t),h._cleanUp(),"function"==typeof s?s(t):void 0}),this.ws.onopen=(f=this,function(){return"function"==typeof f.debug&&f.debug("Web Socket Opened..."),u["accept-version"]=i.VERSIONS.supportedVersions(),u["heart-beat"]=[f.heartbeat.outgoing,f.heartbeat.incoming].join(","),f._transmit("CONNECT",u)})},r.prototype.disconnect=function(t,n){return null==n&&(n={}),this._transmit("DISCONNECT",n),this.ws.onclose=null,this.ws.close(),this._cleanUp(),"function"==typeof t?t():void 0},r.prototype._cleanUp=function(){if(this.connected=!1,this.pinger&&i.clearInterval(this.pinger),this.ponger)return i.clearInterval(this.ponger)},r.prototype.send=function(t,n,e){return null==n&&(n={}),null==e&&(e=""),n.destination=t,this._transmit("SEND",n,e)},r.prototype.subscribe=function(t,n,e){var i;return null==e&&(e={}),e.id||(e.id="sub-"+this.counter++),e.destination=t,this.subscriptions[e.id]=n,this._transmit("SUBSCRIBE",e),i=this,{id:e.id,unsubscribe:function(){return i.unsubscribe(e.id)}}},r.prototype.unsubscribe=function(t){return delete this.subscriptions[t],this._transmit("UNSUBSCRIBE",{id:t})},r.prototype.begin=function(t){var n,e;return e=t||"tx-"+this.counter++,this._transmit("BEGIN",{transaction:e}),n=this,{id:e,commit:function(){return n.commit(e)},abort:function(){return n.abort(e)}}},r.prototype.commit=function(t){return this._transmit("COMMIT",{transaction:t})},r.prototype.abort=function(t){return this._transmit("ABORT",{transaction:t})},r.prototype.ack=function(t,n,e){return null==e&&(e={}),e["message-id"]=t,e.subscription=n,this._transmit("ACK",e)},r.prototype.nack=function(t,n,e){return null==e&&(e={}),e["message-id"]=t,e.subscription=n,this._transmit("NACK",e)},r}(),i={VERSIONS:{V1_0:"1.0",V1_1:"1.1",V1_2:"1.2",supportedVersions:function(){return"1.1,1.0"}},client:function(t,e){var r,o;return null==e&&(e=["v10.stomp","v11.stomp"]),o=new(r=i.WebSocketClass||WebSocket)(t,e),new n(o)},over:function(t){return new n(t)},Frame:e},"undefined"!=typeof exports&&null!==exports&&(exports.Stomp=i),"undefined"!=typeof window&&null!==window?(i.setInterval=function(t,n){return window.setInterval(n,t)},i.clearInterval=function(t){return window.clearInterval(t)},window.Stomp=i):exports||(self.Stomp=i)}).call(this);
