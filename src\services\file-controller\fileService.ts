import { Endpoints } from "@/enums/endPointEnums";

import { AxiosResponse } from "axios";
import { httpClient } from "../httpclient";
import { SearchParmasType } from "@/types";
import { ReportType } from "@/store/searchParamsStore";
import { REPORT } from "@/constants/report";

interface DownloadReportParams {
  startDate: SearchParmasType;
  endDate: SearchParmasType;
  fileName?: string;
  typeReport: ReportType;
}
export class FileService {
  static async getSvgUrl({ id }: { id: string }): Promise<string> {
    const response: AxiosResponse<Blob> = await httpClient.get(`${Endpoints.FILE}/${id}`, {
      responseType: "blob",
    });
    const blob = response.data;
    return URL.createObjectURL(blob);
  }

  static async downloadSvgFile({
    id,
    fileName = "download.svg",
  }: {
    id: string;
    fileName?: string;
  }): Promise<void> {
    try {
      const response: AxiosResponse<Blob> = await httpClient.get(`${Endpoints.FILE}/${id}`, {
        responseType: "blob",
      });
      const blob = response.data;
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error(`Error downloading SVG for id ${id}:`, error);
      throw error;
    }
  }

  static async downloadReportFile({
    startDate,
    endDate,
    typeReport,
    fileName = "report.xlsx",
  }: DownloadReportParams): Promise<void> {
    try {
      const params = new URLSearchParams({
        startDate: startDate ? startDate.toString() : "",
        endDate: endDate ? endDate.toString() : "",
      });

      const reportEndpoints = {
        [REPORT.ticket_finance]: Endpoints.REPORT_EXCEL_FINANCE,
        [REPORT.finance]: Endpoints.REPORT_EXCEL_FINANCE,
        [REPORT.statistics]: Endpoints.REPORT_EXCEL_STATISTICS,
        [REPORT.general]: Endpoints.REPORT_GENERAL,
        [REPORT.by_date]: Endpoints.REPORT_BY_DATE_EXCEL,
      };

      console.log(typeReport, "report");

      const base = reportEndpoints[typeReport] || Endpoints.REPORT_EXCEL_TICKET;
      const url = `${base}?${params.toString()}`;

      const response: AxiosResponse<Blob> = await httpClient.get(url, {
        responseType: "blob",
      });

      await FileService.downloadFile(response, fileName);
    } catch (error) {
      console.error(`Error downloading report from :`, error);
      throw error;
    }
  }

  private static async downloadFile(
    response: AxiosResponse<Blob>,
    defaultFileName: string,
  ): Promise<void> {
    const disposition = response.headers["content-disposition"];
    let fileName = defaultFileName;

    if (disposition && disposition.includes("attachment")) {
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
      const matches = filenameRegex.exec(disposition);
      if (matches != null && matches[1]) {
        fileName = matches[1].replace(/['"]/g, "");
      }
    }

    const blob = response.data;
    const blobUrl = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = blobUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(blobUrl);
  }
}
