import { Endpoints } from "@/enums/endPointEnums";
import {
  ReportResponseType,
  SearchParmasType,
  TVisitGetResponse,
  TVisitPostRequestBody,
} from "@/types";
import { AxiosResponse } from "axios";
import { httpClient } from "../httpclient";

export class Visit {
  static async get(): Promise<TVisitGetResponse> {
    const response: AxiosResponse<TVisitGetResponse> = await httpClient.get(`${Endpoints.VISIT}`);
    return response.data;
  }

  static async post(postData: TVisitPostRequestBody | null): Promise<Blob> {
    const response: AxiosResponse<Blob> = await httpClient.post(`${Endpoints.VISIT}`, postData, {
      responseType: "blob",
    });
    return response.data;
  }
  static async getReportServices(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_SERVICES}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }
  static async getReportFormats(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_FORMATS}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }
  static async getReportCategories(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_CATEGORIES}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }
  static async getReportServicesStatistic(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_SERVICES_STATISTIC}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }
  static async getReportFormatsStatistic(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_FORMATS_STATISTIC}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }
  static async getReportCategoriesStatistic(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_CATEGORIES_STATISTIC}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }
  // ticket report services
  static async getTicketReportServices(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_SERVICES_TICKET_STATISTIC}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }
  static async getTicketReportFormat(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_FORMATS_TICKET_STATISTIC}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }
  static async getTicketReportCategories(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_CATEGORIES_TICKET_STATISTIC}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }

  // . ticket report end here

  // . COUNT-MONEY start khere

  static async getCountMoneyFormat(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_COUNT_MONEY_FORMATS}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }
  static async getCountMoneyCategories(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_COUNT_MONEY_CATEGORIES}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }
  static async getCountMoneyServices(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
  ): Promise<ReportResponseType> {
    const response: AxiosResponse<ReportResponseType> = await httpClient.get(
      `${Endpoints.REPORT_COUNT_MONEY_SERVICES}?startDate=${startDate}&endDate=${endDate}`,
    );
    return response.data;
  }
  //  COUNT-Money end here
}
