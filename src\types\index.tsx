// t-000 common types

import { PageType } from "@/enums/page-type";
import { Dayjs } from "dayjs";

type TPageable = {
  offset: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  pageSize: number;
  paged: boolean;
  pageNumber: number;
  unpaged: boolean;
};
type TSort = {
  empty: boolean;
  sorted: boolean;
  unsorted: boolean;
};
//  common types end

// t--001 - start// here goes navigationResponse types and dependent types
export type NavigationResponseType = {
  totalElements: number;
  totalPages: number;
  size: number;
  content: TContent[];
  number: number;
  sort: TSort;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  pageable: TPageable;
  empty: boolean;
};

type TFile = {
  uuid: string;
  name: string;
  originalName: string;
  fileExtensionName: string;
  fileTypeName: string;
  path: string;
  uploadDate: string;
};
type TVisit = keyof typeof PageType;

export type TContent = {
  id: number;
  name: string;
  activePhoto: TFile;
  deActivePhoto: TFile;
  visitType: TVisit;
  visitTypeName: string;
  activePhotoUrl?: string;
  deActivePhotoUrl?: string;
};
// t--001 - end// navigationResponse types and dependent types

// t--002 - start // here start visit category controller types
export type TVisitCategory = {
  totalPages: number;
  totalElements: number;
  size: number;
  content: TContentVisitCategory[];
  number: number;
  sort: TSort;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  pageable: TPageable;
  empty: boolean;
};
export type TContentVisitCategory = {
  id: number;
  name: string;
  pricePerPerson: number;
  visitFormat: TVisitFormat;
  visitCategoryPriceConditions: TVisitCategoryConditions[];
};
type TVisitFormat = {
  id: string;
  name: string;
};
export type TVisitCategoryConditions = {
  id: number;
  from: number;
  to: number;
  price: number;
};
export type TResponseVisitCategory = {
  id: number;
  name: string;
  pricePerPerson: number;
  visitFormat: TVisitFormat;
  visitCategoryPriceConditions: TVisitCategoryConditions[];
};
export type TrequestBodyPost = {
  name: string;
  enabled: boolean;
  visitFormat: number;
  pricePerPerson: number;
  visitCategoryPriceConditions: TVisitCategoryConditions[];
};

// t--002 - end // here end visit category controller types

// t--003 start // here service controller types
export type TGetService = {
  totalPages: number;
  totalElements: number;
  size: number;
  content: TServiceContent[];

  number: number;
  sort: TSort;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  pageable: TPageable;
  empty: boolean;
};
export type TServiceContent = {
  id: number;
  name: string;
  price: number;
  visitFormats: TVisitFormat[];
};
export type TrequestBodyPutService = {
  name: string;
  enabled: boolean;
  price: number;
};
export type TResponseBodyService = {
  id: number;
  name: string;
  price: number;
  visitFormats: TVisit[];
};

// t--003 end // here service controller types

// t--004 start // here device controller types
export type TResponseDeviceController = {
  totalElements: number;
  totalPages: number;
  first: boolean;
  last: boolean;
  size: number;
  content: TContentResponseDevice[];
  number: number;
  sort: TSort;
  numberOfElements: number;
  pageable: TPageable;
  empty: boolean;
};
type TContentResponseDevice = {
  id: number;
  name: string;
  ipAddress: string;
  password: string;
  pointer: number;
};
export type TRequestDevice = {
  name: string;
  ipAddress: string;
  port: number;
  password: string;
};

export type TResponseDevice = {
  id: number;
  name: string;
  ipAddress: string;
  password: string;
  pointer: number;
};
// t--004 end // here device controller types
// t--005 start // here visit controller types
export type TVisitGetResponse = {
  totalElements: number;
  totalPages: number;
  first: boolean;
  last: boolean;
  size: number;
  content: TContentVisit[];
  number: number;
  sort: TSort;
  numberOfElements: number;
  pageable: TPageable;
  empty: boolean;
};

type TContentVisit = {
  mobilePhone: string;
  visitors: TVisitor[];
  services: TService[];
  totalPaidPriceByCategories: number;
  totalPaidPriceForServices: number;
  event: TEventVisit;
};
type TEventVisit = {
  id: number;
  name: string;
  eventDateTime: Dayjs;
};
type TVisitor = {
  visitCategory: {
    id: number;
    name: string;
    pricePerPerson: number;
    visitFormat: TVisitFormat;
    visitCategoryPriceConditions: TVisitCategoryConditions[];
  };
  numberOfVisitors: number;
  priceForVisitCategory: number;
  events: TEventVisit[];
};
type TService = {
  id: string;
  name: string;
};

export type TVisitPostRequestBody =
  | {
      visitFormat: number;
      services: number[];
      event: {
        name: string;
        start: Dayjs | null;
        end: Dayjs | null;
      };
      mobilePhoneNumber: string;
      visitors: {
        visitCategory: number;
        numberOfVisitors: number;
      }[];
    }
  | {
      visitFormat: number;
      services: number[];
      mobilePhoneNumber: string;
      visitors: {
        visitCategory: number;
        numberOfVisitors: number;
      }[];
    };

export type TResponseVisitorPost = {
  mobilePhone: string;
  visitors: TVisitor[];
  services: TService[];
  totalPaidPriceByCategories: number;
  totalPaidPriceForServices: number;
  event: TEventVisit;
};

export type ReportResponseType = {
  total: number;
  grandTotalCount?: number;
  grandTotalMoney?: number;
  list: ResponseItemType[];
};

type ResponseItemType = {
  name: string;
  count: number;
  totalCount?: number;
  totalMoney?: number;
};

// t--005 end // here visit controller types

// t--006 // auth controller start here

export type RequestAuthServicePostType = {
  username: string;
  password: string;
};

export type ResponseAuthServicePostType = {
  name: string | null;
  userId: number | null;
  surname: string | null;
};

// t--007 // auth controller end here

//
export type SearchParmasType = Dayjs | null | string;
export type dateTyppe = Dayjs | null;

// t-008 // notification controller start here

export type TGetNotificationService = {
  totalPages: number;
  totalElements: number;
  size: number;
  content: TGetNotifications[];

  number: number;
  sort: TSort;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  pageable: TPageable;
  empty: boolean;
};

export type TGetNotifications = {
  id: number;
  title: string;
  description: string;
  isRead: true;
  notificationPriority: "high" | "low";
};

//  notification controller end here

export type TResponse = {
  status: number;
};

// report controller type start here
export type TGetReportController = {
  totalPages: number;
  totalElements: number;
  size: number;
  content: TGetReportContent[];

  number: number;
  sort: TSort;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  pageable: TPageable;
  empty: boolean;
};

export type TGetReportContent = {
  code: string;
  regDate: string | Dayjs;
  userName: string;
  userSurname: string;
  categoryName: string;
  formatName: string;
  entryExitTimestamp: string | Dayjs;
  doorNumber: number;
  visitId: number;
  eventType: number;
  serviceCount: number;
  totalServicePrice: number;
};

// report controller type end here

// report by date controller type start here
export type TGetReportByDateController = {
  totalPages: number;
  totalElements: number;
  size: number;
  content: TGetReportByDateContent[];

  number: number;
  sort: TSort;
  first: boolean;
  last: boolean;
  numberOfElements: number;
  pageable: TPageable;
  empty: boolean;
};

export type TGetReportByDateContent = {
  date: string; //Tarix
  numberOfTickets: number; // Biletlerin sayi
  totalTicketPrice: number; //Biletlerin satish uzre dovriyyesi
  numberOfVisitors: number; //Ziyaretci sayi
  numberOfServices: number; //Elave xidmetlerin sayi
  totalServicePrice: number; //Elave xidmetler uzre dovriyye
};
