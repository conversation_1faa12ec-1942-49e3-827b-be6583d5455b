import { useAuthStore } from "@/store/authStore";
import { Button } from "antd";
import { useState } from "react";
import ExitIcon from "../icons/ExitIcon";
import ConfirmationModal from "./ConfirmationModal/ConfirmationModal";
import { Auth } from "@/services/auth-controller/auth";

const ExitButton = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { clearUser } = useAuthStore();

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
    Auth.logout();
    clearUser();
    window.location.href = "/";
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <Button type='text' icon={<ExitIcon />} size={"small"} onClick={showModal}>
        Çıxış et
      </Button>
      <ConfirmationModal
        isModalOpen={isModalOpen}
        handleCancel={handleCancel}
        handleOk={handleOk}
      />
    </>
  );
};

export default ExitButton;
