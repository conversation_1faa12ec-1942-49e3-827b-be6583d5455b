export const getTitleUtil = ({
  isFinance,
  isStatistic,
}: {
  isFinance: boolean;
  isStatistic: boolean;
}) => {
  const titles = {
    finance: {
      formatTitle: "Ümumi gəlir",
      categoryTitle: "Kateqoriyalar üzrə məbləğ",
      serviceTitle: "Əlavə xidmətlər üzrə məbləğ",
    },
    statistic: {
      formatTitle: "Ümumi ziyarətçi sayı",
      categoryTitle: "Kateqoriyalar üzrə ziyarətçi sayı",
      serviceTitle: "Əlavə xidmətlərdən yararlanan ziyarətçi sayı",
    },
    default: {
      formatTitle: "Ümumi bilet sayı",
      categoryTitle: "Kateqoriyalar üzrə bilet sayı",
      serviceTitle: "Əlavə xidmətlər tətbiq edilən bilet sayı",
    },
  };

  const typeKey = isFinance ? "finance" : isStatistic ? "statistic" : "default";
  return titles[typeKey];
};
