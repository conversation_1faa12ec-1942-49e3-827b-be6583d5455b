import { PageType } from "@/enums/page-type";
import { Visitors } from "@/store/visitorsStore";

export const checkIsDisabled = (pageType: string, visitors: Visitors[], eventName: string) => {
  return pageType === PageType.INDIVIDUAL
    ? !visitors.length || !visitors.some((item) => item.numberOfVisitors > 0)
    : !visitors.length || !visitors.some((item) => item.numberOfVisitors > 0) || !eventName;
};
