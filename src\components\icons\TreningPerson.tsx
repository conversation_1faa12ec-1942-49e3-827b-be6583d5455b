import { colors } from "@/constants/colors";
import { IconProps } from "@/types/iconTypes/icon-types";
import { FC } from "react";

const TreningPerson: FC<IconProps> = ({ active }) => {
  const colorIcon = active ? colors.main_green : colors.white;
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='36' height='36' viewBox='0 0 36 36' fill='none'>
      <g clipPath='url(#clip0_286_6048)'>
        <path
          d='M9.76752 9.20547C9.43937 9.22508 9.18407 9.49607 9.18407 9.82485V14.7457H3.09697V10.4492C3.09697 10.1868 2.88428 9.97405 2.62187 9.97405C2.35947 9.97405 2.1467 10.1867 2.1467 10.4492V16.4964C1.37671 16.4889 0.73792 15.9619 0.698053 15.1621C0.58499 12.8969 0.534154 11.1373 0.587592 8.87718C0.625068 8.38998 0.70185 8.06971 0.855623 7.82593C1.14376 7.36918 1.75204 7.14151 2.24964 7.007C3.38666 6.69967 5.0402 6.29516 5.93746 6.29516C7.49242 6.29516 8.96399 6.6662 10.3238 6.59926C12.0553 6.51404 13.0808 5.49529 13.9724 3.90608C14.5577 2.86286 16.2478 3.71293 15.7252 4.80749C14.664 7.02986 13.1692 9.00156 9.76759 9.2054L9.76752 9.20547ZM3.0969 26.6408H5.45842L6.03772 19.5876C6.04215 19.534 6.08666 19.493 6.14045 19.493C6.19424 19.493 6.23882 19.534 6.24325 19.5876L6.82255 26.6408H9.184V15.73H3.09697V26.6408H3.0969ZM8.13233 4.66194C9.08183 3.19009 8.74222 0.757629 6.36398 0.757629C3.94558 0.757629 3.63522 3.27292 4.64476 4.7357C5.59363 6.11038 7.23655 6.05061 8.13233 4.66194ZM29.7566 25.9157C30.3792 25.9164 31.0042 25.5895 31.4647 24.9224C32.4678 23.4691 32.1594 20.9699 29.7566 20.9699C27.3538 20.9699 27.0453 23.4691 28.0485 24.9224C28.509 25.5895 29.134 25.9164 29.7566 25.9157ZM34.438 27.5861C33.9961 27.3269 33.2235 27.1223 32.5181 26.9419L30.7778 26.5138C30.4267 26.4275 30.0916 26.3843 29.7566 26.3843C29.4216 26.3843 29.0865 26.4275 28.7354 26.5138L26.9951 26.9419C26.2898 27.1223 25.5172 27.3269 25.0752 27.5861C24.4987 27.9241 24.2932 28.2692 24.2226 29.1874C24.1333 30.8098 24.163 32.3177 24.2283 33.8675C24.2608 34.6362 24.8931 35.2425 25.6626 35.2425H25.7019V30.9597C25.7019 30.6879 25.9222 30.4675 26.1941 30.4675C26.4659 30.4675 26.6863 30.6879 26.6863 30.9597V35.2425H32.8269V30.9597C32.8269 30.6879 33.0472 30.4675 33.319 30.4675C33.5909 30.4675 33.8112 30.6879 33.8112 30.9597V35.2425H33.8505C34.62 35.2425 35.2523 34.6362 35.2848 33.8675C35.3502 32.3177 35.3798 30.8098 35.2905 29.1874C35.2199 28.2692 35.0144 27.9241 34.4379 27.5861H34.438ZM13.5182 2.91791H12.1006V4.82844C12.4951 4.44854 12.8137 3.95973 13.114 3.42451C13.2229 3.23038 13.3592 3.05987 13.5182 2.91791ZM16.6134 5.23175C17.0357 4.34743 16.7273 3.44687 16.0843 2.91791H34.298V17.1257H12.1006V9.75679C14.1872 9.03278 15.5479 7.46312 16.6134 5.23175ZM20.3821 14.9969C20.3821 14.7251 20.1617 14.5047 19.8899 14.5047H14.4053C14.1334 14.5047 13.9131 14.7251 13.9131 14.9969C13.9131 15.2687 14.1334 15.4891 14.4053 15.4891H19.8899C20.1617 15.4891 20.3821 15.2687 20.3821 14.9969ZM20.3821 13.0985C20.3821 12.8266 20.1617 12.6063 19.8899 12.6063H14.4053C14.1334 12.6063 13.9131 12.8266 13.9131 13.0985C13.9131 13.3703 14.1334 13.5906 14.4053 13.5906H19.8899C20.1617 13.5906 20.3821 13.3703 20.3821 13.0985ZM32.4584 14.9969C32.4584 14.7251 32.2381 14.5047 31.9662 14.5047H24.4514C24.1796 14.5047 23.9593 14.7251 23.9593 14.9969C23.9593 15.2687 24.1796 15.4891 24.4514 15.4891H31.9662C32.2381 15.4891 32.4584 15.2687 32.4584 14.9969ZM32.4584 13.0985C32.4584 12.8266 32.2381 12.6063 31.9662 12.6063H24.4514C24.1796 12.6063 23.9593 12.8266 23.9593 13.0985C23.9593 13.3703 24.1796 13.5906 24.4514 13.5906H31.9662C32.2381 13.5906 32.4584 13.3703 32.4584 13.0985ZM31.9662 10.7078H27.9216C27.6498 10.7078 27.4295 10.9282 27.4295 11.2C27.4295 11.4718 27.6498 11.6922 27.9216 11.6922H31.9662C32.2381 11.6922 32.4584 11.4718 32.4584 11.2C32.4584 10.9282 32.2381 10.7078 31.9662 10.7078ZM14.4195 10.7789C14.1995 10.9382 14.1497 11.2459 14.3087 11.4662C14.4049 11.5994 14.5554 11.6703 14.7086 11.6703C14.8086 11.6703 14.9089 11.6401 14.9963 11.577L21.7243 6.71907L23.9303 10.6233C24.0011 10.7481 24.1221 10.8362 24.2628 10.8643C24.4034 10.8921 24.5488 10.8575 24.662 10.7691L30.4835 6.22021L30.3265 6.81737C30.2574 7.07999 30.4143 7.34914 30.6773 7.41826C30.9403 7.4871 31.2094 7.32981 31.2786 7.06684L31.8052 5.05914C31.8438 4.91275 31.8123 4.75651 31.7201 4.63586C31.6287 4.5152 31.4859 4.44369 31.3341 4.44236L29.259 4.42C29.257 4.42 29.2549 4.41965 29.2536 4.41965C28.9841 4.41965 28.7642 4.63684 28.7615 4.90663C28.7584 5.17846 28.9763 5.40114 29.2484 5.40416L29.9192 5.41126L24.5057 9.64154L22.3106 5.75629C22.2419 5.63429 22.1242 5.54753 21.9873 5.51736C21.8508 5.48755 21.7074 5.51736 21.5942 5.59942L14.4195 10.7789H14.4195ZM34.8638 0.79377H11.5348C11.2214 0.79377 10.9649 1.0502 10.9649 1.36365C10.9649 1.67718 11.2214 1.93354 11.5348 1.93354H34.8638C35.1773 1.93354 35.4337 1.67711 35.4337 1.36365C35.4337 1.0502 35.1773 0.79377 34.8638 0.79377ZM35.4337 18.68C35.4337 18.9935 35.1773 19.2499 34.8638 19.2499H23.6779V20.8649C24.41 21.0784 24.946 21.7549 24.946 22.555C24.946 23.5253 24.1567 24.3149 23.1854 24.3149C22.2141 24.3149 21.4255 23.5252 21.4255 22.555C21.4255 21.7548 21.9619 21.0783 22.6936 20.8648V19.2499H11.5348C11.2213 19.2499 10.9649 18.9935 10.9649 18.68C10.9649 18.3666 11.2213 18.1101 11.5348 18.1101H34.8637C35.1772 18.1101 35.4336 18.3666 35.4336 18.68H35.4337ZM23.1854 21.7789C22.758 21.7789 22.41 22.1269 22.41 22.555C22.41 22.9831 22.758 23.3308 23.1854 23.3308C23.6128 23.3308 23.9619 22.9827 23.9619 22.555C23.9619 22.1273 23.6138 21.7789 23.1854 21.7789ZM16.6149 25.9157C17.2375 25.9164 17.8625 25.5895 18.323 24.9224C19.3262 23.4691 19.0177 20.9699 16.6149 20.9699C14.2121 20.9699 13.9037 23.4691 14.9068 24.9224C15.3673 25.5895 15.9923 25.9164 16.6149 25.9157ZM21.2963 27.5861C20.8544 27.3269 20.0818 27.1223 19.3764 26.9419L17.6361 26.5138C17.285 26.4275 16.9499 26.3843 16.6149 26.3843C16.2799 26.3843 15.9448 26.4275 15.5937 26.5138L13.8534 26.9419C13.1481 27.1223 12.3755 27.3269 11.9335 27.5861C11.357 27.9241 11.1516 28.2692 11.081 29.1874C10.9916 30.8098 11.0213 32.3177 11.0867 33.8675C11.1191 34.6362 11.7515 35.2425 12.5209 35.2425H12.5602V30.9597C12.5602 30.6879 12.7806 30.4675 13.0524 30.4675C13.3242 30.4675 13.5446 30.6879 13.5446 30.9597V35.2425H19.6852V30.9597C19.6852 30.6879 19.9055 30.4675 20.1774 30.4675C20.4492 30.4675 20.6695 30.6879 20.6695 30.9597V35.2425H20.7089C21.4783 35.2425 22.1106 34.6362 22.1431 33.8675C22.2085 32.3177 22.2381 30.8098 22.1488 29.1874C22.0782 28.2692 21.8727 27.9241 21.2962 27.5861H21.2963Z'
          fill={colorIcon}
        />
      </g>
      <defs>
        <clipPath id='clip0_286_6048'>
          <rect width='36' height='36' fill={colorIcon} />
        </clipPath>
      </defs>
    </svg>
  );
};

export default TreningPerson;
