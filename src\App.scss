* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
#root {
  position: relative;
  width: 100%;
  min-height: 100vh;
}
body {
  background-color: var(--bg-body);
  /* Targeting WebKit-based browsers: Chrome, Safari, and Edge */
  ::-webkit-scrollbar {
    width: 8px; /* Scrollbar width */
    height: 8px; /* Scrollbar height (for horizontal scrollbar) */
  }

  ::-webkit-scrollbar-track {
    background-color: #f1f1f1; /* Track color */
  }

  ::-webkit-scrollbar-thumb {
    background-color: #c1c1c1 !important; /* Scroll handle color */
    border-radius: 4px !important; /* Make the scroll handle rounded */
    border: 2px solid #f1f1f1 !important; /* Optional: to add spacing around handle */
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #a1a1a1; /* Handle color on hover */
  }
}
.ant-notification-notice-wrapper {
  transform: translate3d(0px, 3.5rem, 0px) !important;
}
.ant-table-tbody > tr > td {
  padding: 0.5rem 0.5rem !important;
}
.ant-table-tbody > tr:nth-child(even) {
  background-color: #e6fffa !important;
}
.apexcharts-legend-text {
  max-width: 300px !important;
}
