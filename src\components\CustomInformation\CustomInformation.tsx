import { InfoCircleOutlined } from "@ant-design/icons";
import { FC } from "react";
import "./CustomInformation.scss";
import { Tooltip } from "antd";

const CustomInformation: FC<PropsCustomInformation> = ({ info, text, tooltip }) => {
  return (
    <>
      <span>{text}</span>
      <span className='information'>
        {info && (
          <Tooltip title={tooltip}>
            <InfoCircleOutlined />
          </Tooltip>
        )}
      </span>
    </>
  );
};

export default CustomInformation;

type PropsCustomInformation = Readonly<{ info?: boolean; text: string; tooltip?: string }>;
