import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

type PhoneNumberSliceType = {
  phoneNumber: string;
  setPhoneNumber: (key: string) => void;
  clearPhoneNumber: () => void;
};

const createPhoneNumberSlice: StateCreator<PhoneNumberSliceType, [["zustand/devtools", never]]> = (
  set,
) => ({
  phoneNumber: "",
  setPhoneNumber: (key) => set({ phoneNumber: key }, false, `PhoneNumberState ${key}`),
  clearPhoneNumber: () => set({ phoneNumber: "" }, false, `PhoneNumberState ${"clearing phone"}`),
});

export const usePhoneNumberStore = create<PhoneNumberSliceType>()(devtools(createPhoneNumberSlice));
