import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

export type Visitors = {
  visitCategory: number;
  numberOfVisitors: number;
  sum: number;
};

type VisitorsSliceType = {
  visitors: Visitors[];
  setVisitors: (key: Visitors) => void;
  clearVisitors: () => void;
};

const createVisitor: StateCreator<VisitorsSliceType, [["zustand/devtools", never]]> = (set) => ({
  visitors: [],
  setVisitors: (newVisitor) =>
    set(
      (state: VisitorsSliceType) => {
        const existingIndex = state.visitors.findIndex(
          (v) => v.visitCategory === newVisitor.visitCategory,
        );

        if (existingIndex > -1) {
          const updatedVisitors = [...state.visitors];
          updatedVisitors[existingIndex] = {
            ...updatedVisitors[existingIndex],
            numberOfVisitors: newVisitor.numberOfVisitors,
            sum: newVisitor.sum,
          };

          return {
            visitors: updatedVisitors,
          };
        } else {
          return {
            visitors: [...state.visitors, newVisitor],
          };
        }
      },
      false,
      `servicesSetState ${newVisitor.visitCategory}`,
    ),
  clearVisitors: () => set({ visitors: [] }, false, `Clear Visitors`),
});

export const useVisitorStore = create<VisitorsSliceType>()(devtools(createVisitor));
