import useVisit from "@/hooks/mutationhooks/useVisitMutation";
import useConfirmationPayload from "@/hooks/useConfirmationPayload";
import { useParams } from "react-router-dom";
import { useEventStore } from "@/store/eventStore";
import { usePageTypeStore } from "@/store/pageTypeStore";
import { useVisitorStore } from "@/store/visitorsStore";
import { checkIsDisabled } from "@/utils/getIsDisabled";
import { useEffect } from "react";
import { Spin } from "antd";
import "./ConfirmButton.scss";

const ConfirmButton = () => {
  const { pageType } = usePageTypeStore();
  const { mutate, isPending } = useVisit();
  const { id } = useParams();
  const { eventName } = useEventStore();
  const { visitors } = useVisitorStore();
  const isDisabled = checkIsDisabled(pageType, visitors, eventName);
  const { payload } = useConfirmationPayload();

  const handleClick = () => {
    if (id) {
      mutate(payload);
    }
  };

  const handleEvent = (e: KeyboardEvent) => {
    if (e.key !== "Enter" || isDisabled || isPending) return;

    const activeElement = document.activeElement;
    if (activeElement?.classList.contains("action-buttons")) {
      return;
    }
    const buttons = document.querySelectorAll<HTMLButtonElement>(".confirm-btn");

    let clicked = false;
    for (const button of buttons) {
      const rect = button.getBoundingClientRect();

      if (rect.top >= 0 && rect.bottom <= window.innerHeight) {
        button.click();
        clicked = true;
        break;
      }
    }

    if (!clicked) {
      e.preventDefault();
    }
  };

  useEffect(() => {
    window.addEventListener("keydown", handleEvent);
    return () => {
      window.removeEventListener("keydown", handleEvent);
    };
  }, [payload, id, isDisabled, isPending]);

  return (
    <div id='confirm-container'>
      {isPending ? (
        <Spin percent={"auto"} size='large' />
      ) : (
        <button
          onClick={handleClick}
          disabled={isDisabled}
          className={`confirm-btn ${isDisabled || isPending ? "disabled" : ""}`}
        >
          Təsdiq et
        </button>
      )}
    </div>
  );
};

export default ConfirmButton;
