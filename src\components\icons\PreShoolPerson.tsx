import { colors } from "@/constants/colors";
import { IconProps } from "@/types/iconTypes/icon-types";
import { FC } from "react";

const PreShoolPerson: FC<IconProps> = ({ active }) => {
  const colorIcon = active ? colors.main_green : colors.white;
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='37' height='36' viewBox='0 0 37 36' fill='none'>
      <g clipPath='url(#clip0_286_6259)'>
        <path
          d='M16.2119 15.6C16.3756 15.5454 16.5937 15.4909 16.7574 15.3818C17.5756 15 18.5028 15.3818 18.8301 16.1454C19.2119 16.9636 18.8301 17.8909 18.0665 18.2182C17.4665 18.4909 16.8119 18.7091 16.2119 18.8182V22.4182H22.6483V14.5636H16.2119V15.6Z'
          fill={colorIcon}
        />
        <path
          d='M10.6482 23.5091C10.3755 24.5455 9.61184 25.3636 8.68457 25.8C9.39366 28.7455 10.3755 29.8364 9.44821 31.1455L10.3755 31.4727C11.6846 31.8546 12.7209 30.7091 12.3937 29.5091L10.6482 23.5091Z'
          fill={colorIcon}
        />
        <path
          d='M11.1941 15.1091C11.9577 15 14.9577 15.2727 17.7941 12.4364C18.2304 12 18.2304 11.3454 17.7941 10.9636C17.3577 10.5273 16.7031 10.5273 16.3213 10.9636C14.6304 12.6 12.7213 13.0364 11.085 13.0364C11.4122 13.8545 11.3031 14.5091 11.1941 15.1091Z'
          fill={colorIcon}
        />
        <path
          d='M29.6849 28.0364L29.9576 26.1818C29.2485 26.1273 28.594 25.9636 27.8849 25.6909L27.5576 27.8727V28.2546L28.1031 30.7091C28.2122 31.2546 28.7576 31.6364 29.3576 31.5273C29.9031 31.4182 30.2849 30.8182 30.1758 30.2727L29.6849 28.0364Z'
          fill={colorIcon}
        />
        <path
          d='M32.9575 28.2L33.3393 25.8C32.6302 26.0182 31.8666 26.1818 31.1575 26.2364L30.8848 28.0909V28.4727L31.4302 30.7636C31.5393 31.3091 32.1393 31.6909 32.6848 31.5273C33.2302 31.4182 33.612 30.8182 33.4484 30.2727L32.9575 28.2Z'
          fill={colorIcon}
        />
        <path
          d='M36.0118 18.9273C36.0118 18.8727 33.4481 14.0182 33.6118 14.2909C33.5572 14.1818 33.4481 14.0727 33.3936 14.0182V13.9636C31.6481 12.9818 29.9027 12.9818 28.1572 13.9636C27.3936 14.3454 26.0299 14.5636 25.539 14.2909C25.1572 14.0727 24.6663 14.1818 24.4481 14.5636C24.2299 14.9454 24.339 15.4364 24.7208 15.6545C25.6481 16.2 27.0663 15.9818 28.1027 15.6545V17.9454L26.9572 24.5454C29.5208 26.0727 32.4118 25.9636 34.7572 24.5454L33.3936 18.0545V17.3454C33.4481 17.4 34.7027 19.8545 34.5936 19.6909C34.7572 20.0727 35.2481 20.2364 35.6299 20.0182C36.0663 19.8 36.2299 19.3091 36.0118 18.9273Z'
          fill={colorIcon}
        />
        <path
          d='M17.8482 17.7818C18.3936 17.5636 18.6118 16.9091 18.3936 16.3636C18.1754 15.8182 17.5209 15.6 16.9754 15.8182C13.9754 17.1818 10.3209 16.0364 8.41179 14.1273C7.97543 13.7454 7.32088 13.7455 6.93907 14.1818C6.55725 14.5636 6.55725 15.2182 6.99361 15.6545C9.39361 18.1091 13.9209 19.5818 17.8482 17.7818Z'
          fill={colorIcon}
        />
        <path d='M15.1211 23.0727H21.5575V30.9818H15.1211V23.0727Z' fill={colorIcon} />
        <path d='M18.3398 5.56363H24.7762V13.4727H18.3398V5.56363Z' fill={colorIcon} />
        <path
          d='M8.03035 25.5273C9.33944 25.2 10.3758 23.7818 10.2667 22.3091C10.2122 21.0545 10.2122 19.7455 10.3213 18.3818C8.13944 17.5091 6.77581 16.2 6.66672 16.0909C6.01217 15.4364 6.06672 14.4545 6.61217 13.8545C7.21217 13.2545 8.19399 13.2 8.84853 13.8C8.90308 13.8545 9.61217 14.5091 10.7031 15.0545C11.0304 12.7636 9.93944 11.3455 8.13944 11.0727C6.39399 10.8 4.81217 12 4.4849 13.6909C4.15763 15.7636 3.39399 21.9818 4.21217 23.7273C4.21217 23.7818 5.35763 27.5455 5.30308 27.4909C5.24853 27.4909 3.12126 26.7273 3.2849 26.7818C2.46672 26.5091 1.59399 26.9455 1.26672 27.7636C0.993989 28.5818 1.43035 29.4545 2.24853 29.7818L7.15763 31.4727C8.41217 31.8545 9.50308 30.7091 9.17581 29.5091C9.17581 29.5091 7.92126 25.2545 8.03035 25.5273Z'
          fill={colorIcon}
        />
        <path
          d='M8.35714 10.6364C10.1026 10.6364 11.4662 9.21818 11.4662 7.52727C11.4662 5.83636 10.048 4.41818 8.35714 4.41818C6.66623 4.41818 5.24805 5.83636 5.24805 7.52727C5.24805 9.21818 6.66623 10.6364 8.35714 10.6364Z'
          fill={colorIcon}
        />
        <path
          d='M30.8302 12.9273C32.1939 12.9273 33.3939 11.7818 33.3939 10.3636C33.3939 8.94544 32.2484 7.79999 30.8302 7.79999C29.4121 7.79999 28.2666 8.94544 28.2666 10.3636C28.2666 11.8364 29.4121 12.9273 30.8302 12.9273Z'
          fill={colorIcon}
        />
      </g>
      <defs>
        <clipPath id='clip0_286_6259'>
          <rect width='36' height='36' fill='white' transform='translate(0.666992)' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default PreShoolPerson;
