.login {
  height: 100vh;
  width: 100%;
  position: relative;
  .main-login {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 10;
    overflow: hidden;
    .login-form-container {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      .login-form {
        width: 37.5rem;
        padding: 2.5rem;

        gap: 2rem;
        border-radius: 2rem;
        border: 1px solid var(--White, #fff);
        background: rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(12px);
        .upper-button-group {
          margin-bottom: 3.25rem;
          width: 100%;
          .buttons {
            width: 40%;
            margin: auto;
            display: flex;
            padding: 0.75rem;
            align-items: flex-start;
            gap: 0.75rem;
            border-radius: 6.1875rem;
            background: rgba(255, 255, 255, 0.3);
            .enter {
              display: flex;
              width: 100%;

              padding: 0.7rem 1.25rem;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              gap: 0.625rem;
              border-radius: 6.1875rem;
              background: var(--Main-color-1000, #0b3221);
              color: white;
              border: none;
            }
            .register {
              display: flex;
              width: 8.5rem;
              padding: 0rem 1.25rem;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              gap: 0.625rem;
              border-radius: 6.1875rem;
              background: var(--Main-color-1000, #50434300);
              color: white;
              border: none;
            }
          }
        }
        .middle-form {
          width: 100%;

          .form-input {
            width: 100%;
            display: flex;
            height: 3.5rem;
            padding: var(--Distance-4x2, 0.5rem) var(--Distance-4x3, 0.75rem);
            align-items: center;
            gap: var(--Distance-4x2, 1.5rem);
            align-self: stretch;
            border-radius: 0.5rem;
            border: 1px solid var(--White, #fff);
            margin-bottom: 1.5rem;
            justify-content: space-between;
            .password-toggle {
              background-color: transparent;
              border: none;
            }

            .label-input {
              width: 5%;
              height: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;
              color: white;
            }
            input {
              width: 95%;
              background: transparent;
              height: 100%;
              border: none;
              outline: none;
              font-size: 1rem;
              color: var(--color-white);
              &::placeholder {
                color: white !important;
              }
            }
            .text-white {
              color: white;
            }
          }
        }
        .bottom-form {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2.5rem;
          .forget {
            color: var(--Secondary-1000);
          }
        }
        .approve {
          width: 100%;
          height: 3.5rem;
          background-color: var(--color-main);
          color: var(--color-white);
          border: none;
        }
        .tools {
          display: flex;
          align-items: center;
          width: 100%;
          justify-content: center;
          gap: 10px;
          .create-account {
            color: var(--Secondary-1000);
            text-decoration: underline;
          }
        }
      }
    }
  }
}
