import { useMemo } from "react";

import useAllRoutes from "./useAllRoutes";
import ProtectedLayoutWithOutlet from "@/hoc/protectedLayoutWithOutlet";

const useRoutesConfig = () => {
  const allRoutes = useAllRoutes();
  return useMemo(
    () => [
      {
        element: <ProtectedLayoutWithOutlet />,
        children: allRoutes,
      },
    ],
    [allRoutes],
  );
};

export default useRoutesConfig;
