import { QueryKeys } from "@/enums/queryKeyEnums";
import { ReportReservedTicketController } from "@/services/report-service/report-reserved-ticket";
import { useQuery } from "@tanstack/react-query";

const useReportReservedTicket = () =>
  useQuery({
    queryKey: [QueryKeys.REPORT_RESERVED_TICKET],
    queryFn: () => ReportReservedTicketController.getReport(),
  });

export default useReportReservedTicket;
