import ThreeDotIcon from "@/components/icons/ThreeDotIcon";
import IconButton from "@/hoc/IconButton/IconButton";
import { NotificationController } from "@/services/notification-controller/notification";
import "./TitleNotification.scss";
import { QueryKeys } from "@/enums/queryKeyEnums";
import { useQueryClient } from "@tanstack/react-query";
const TitleNotification = () => {
  const queryClient = useQueryClient();
  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: [QueryKeys.NOTIFICATIONS] });
  };
  const handleReadAll = async () => {
    await NotificationController.readAll();
    handleRefresh();
  };
  return (
    <div className='title-notification'>
      <p className='title'>Bildirişlər</p>
      <div style={{ cursor: "pointer" }}>
        <IconButton onClick={handleReadAll}>
          <ThreeDotIcon />
        </IconButton>
      </div>
    </div>
  );
};

export default TitleNotification;
