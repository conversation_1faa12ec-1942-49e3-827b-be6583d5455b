import { Endpoints } from "@/enums/endPointEnums";
import { AxiosResponse } from "axios";
import { httpClient } from "../httpclient";

export class ReportReservedTicketController {
  static async getReport(): Promise<Record<string, number>> {
    const response: AxiosResponse<Record<string, number>> = await httpClient.get(
      `${Endpoints.REPORT_RESERVED_TICKET}`,
    );
    return response.data;
  }
}
