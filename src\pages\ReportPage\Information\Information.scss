.dashboard-stats {
  width: 100%;
  padding: 1.5rem;
  font-family: Arial, sans-serif;
  color: #333;
  .top-info {
    margin-bottom: 2rem;

    h2 {
      font-size: 1.625rem;
      font-weight: bold;
      margin-bottom: 1rem;
      border: 1px solid var(--Secondary-200, #ccf3d2);
      background: var(--Secondary-50, #f2fcf4);
      display: flex;
      padding: 0.75rem;
      align-items: center;
      gap: 0.25rem;
      align-self: stretch;
      justify-content: space-between;
      span {
        color: #2ea44f;
      }
    }

    .summary-cards {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .summary-card {
      padding: 1rem;
      height: 100px;
      flex: 1 1 200px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      border-left: 1px solid rgba(0, 0, 0, 0.1);
      &:first-child {
        border-left: none;
      }

      h3 {
        font-size: 1.25rem;
        margin: 0 0 0.5rem;
        color: #444;
      }

      p {
        font-size: 0.95rem;
        margin: 0 0 0.25rem;
        line-height: 1.2;
        color: #666;
      }
      .tool-wrap {
        span {
          color: var(--Main-color-1000, #0b3221);
          font-family:
            "Public Sans" Arial,
            Helvetica,
            sans-serif;
          font-size: 1.25rem;
          font-style: normal;
          font-weight: 700;
          line-height: 2.25rem;
        }
        .index {
          color: var(--Secondary-1000, #00c31f);
          font-family:
            "Public Sans" Arial,
            Helvetica,
            sans-serif;
          font-size: 0.85rem;
          font-style: normal;
          font-weight: 600;
          line-height: 1.25rem;
        }
      }
    }
  }

  .charts-section {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 0.25rem;

    .chart-container {
      height: auto;
      flex: 1 1 500px;
      background-color: #fff;
      border-radius: 8px;
      padding: 1rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);

      h3 {
        margin-bottom: 1rem;
        font-size: 1.1rem;
      }

      .chart-wrapper {
        width: 100%;

        .chart-legend {
          flex: 1 1 200px;

          .legend-item {
            display: flex;
            align-items: center;
            margin: 0.25rem 0;

            .legend-color {
              display: inline-block;
              width: 12px;
              height: 12px;
              background-color: #ccc;
              border-radius: 50%;
              margin-right: 0.5rem;
            }

            p {
              font-size: 0.9rem;
              color: #555;
              margin: 0;
            }
          }
        }
      }

      .chart-total {
        margin-top: 1rem;
        font-weight: bold;
        color: #444;
      }
    }
  }
}
