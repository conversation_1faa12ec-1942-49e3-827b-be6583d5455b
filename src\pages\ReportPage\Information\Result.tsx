import TruncateTooltip from "@/components/TruncatedTooltip/TruncatedTooltip";
import { useInformationStore } from "@/store/informationStore";
import { formatNumber, formatThousand } from "@/utils/formatNumber";
import Chart from "react-apexcharts";
import { getTitleUtil } from "./utils";
import { REPORT } from "@/constants/report";
import { useSearchParamsStore } from "@/store/searchParamsStore";
import ReservedInfoTicket from "@/components/ReservedTicketInfo/ReservedInfoTicket";

const Result = () => {
  const { categories, formats, services } = useInformationStore();
  const { reportType } = useSearchParamsStore();
  const isFinance = reportType === REPORT.finance;
  const isStatistic = reportType === REPORT.statistics;
  const { categoryTitle, serviceTitle } = getTitleUtil({ isStatistic, isFinance });
  return (
    <div className='dashboard-stats'>
      <div className='top-info'>
        <h2>
          <div>
            {typeof formats?.grandTotalMoney === "number" &&
              typeof services?.grandTotalMoney === "number" &&
              `Nəticə : ${formatNumber((formats?.grandTotalMoney || 0) + (services?.grandTotalMoney || 0))} ₼`}
            <span>
              {" "}
              - ({formats?.grandTotalCount ? formatThousand(formats?.grandTotalCount) : 0} bilet)
            </span>
          </div>
          <ReservedInfoTicket />
        </h2>

        <div className='summary-cards'>
          {formats?.list.map((item, index) => (
            <div className='summary-card' key={index}>
              <div className='tool-wrap'>
                <span>
                  {item?.totalMoney && formatNumber(item?.totalMoney?.toString())} ₼ (
                  {item?.totalCount ? formatThousand(item?.totalCount) : 0} bilet){" "}
                </span>
                <span className='index'>
                  {item?.totalMoney &&
                    formats?.grandTotalMoney &&
                    ((item?.totalMoney / formats?.grandTotalMoney) * 100).toFixed(2).toString()}
                  %
                </span>
              </div>
              <TruncateTooltip text={item.name} />
            </div>
          ))}
        </div>
      </div>
      {formats?.grandTotalCount === 0 ? (
        <></>
      ) : (
        <div className={`charts-section`}>
          <div className={`chart-container`}>
            <h3>{categoryTitle}</h3>
            <div className='chart-wrapper'>
              <div className='chart-legend'>
                <p className='chart-total'>
                  Ümumi:
                  {` ${categories?.grandTotalMoney && formatNumber(categories?.grandTotalMoney?.toString())} ₼ - (${categories?.grandTotalCount ? formatThousand(categories?.grandTotalCount) : 0} bilet) `}
                </p>
              </div>
              {categories?.grandTotalCount && (
                <Chart
                  options={{
                    labels: categories
                      ? categories?.list?.map(
                          (item) =>
                            ` ${item.name}: ${item.totalMoney ? formatNumber(item.totalMoney?.toString()) : 0}₼ (${item.totalCount ? formatThousand(item.totalCount) : 0} - bilet) `,
                        )
                      : [],
                    legend: {
                      show: true,
                      position: "right",
                      horizontalAlign: "left",
                      fontSize: "13px",
                      width: 400,
                      markers: {
                        strokeWidth: 4,
                        shape: "circle",
                      },
                      itemMargin: { vertical: 5 },
                    },
                    dataLabels: {
                      enabled: true,
                      style: {
                        fontSize: "13px",
                        colors: ["#000"],
                      },
                      dropShadow: {
                        enabled: false,
                      },
                    },
                    tooltip: {
                      y: {
                        formatter: () => ``,
                      },
                    },
                    plotOptions: {
                      pie: {
                        customScale: 0.9,
                        donut: {
                          size: "35%",
                        },
                      },
                    },
                    chart: {
                      toolbar: {
                        show: true,
                        tools: {
                          download: true,
                        },
                      },
                    },
                  }}
                  series={categories?.list.map((item) => item.totalCount ?? 0) || []}
                  type='donut'
                  width='100%'
                />
              )}
            </div>
          </div>

          <div className='chart-container'>
            <h3>{serviceTitle}</h3>
            <div className='chart-wrapper'>
              <div className='chart-legend'>
                <p className='chart-total'>
                  Ümumi:
                  {` ${services?.grandTotalMoney && formatNumber(services?.grandTotalMoney?.toString())} ₼ - (${services?.grandTotalCount ? formatThousand(services?.grandTotalCount) : 0} bilet) `}
                </p>
              </div>
              {services?.grandTotalCount === 0 ? (
                ""
              ) : (
                <Chart
                  options={{
                    labels: services
                      ? services?.list?.map(
                          (item) =>
                            `${item.name}: ${item.totalMoney ? formatThousand(item.totalMoney?.toString()) : 0}₼ (${item.totalCount ? formatThousand(item.totalCount) : 0} - bilet)`,
                        )
                      : [],
                    legend: {
                      show: true,
                      position: "right",
                      horizontalAlign: "left",
                      fontSize: "13px",
                      width: 400,
                    },
                    dataLabels: {
                      enabled: true,
                      style: {
                        fontSize: "13px",
                        colors: ["#000"],
                      },
                      dropShadow: {
                        enabled: false,
                      },
                    },
                    tooltip: {
                      y: {
                        formatter: () => ``,
                      },
                    },
                    plotOptions: {
                      pie: {
                        customScale: 0.9,
                        donut: {
                          size: "35%",
                        },
                      },
                    },
                    chart: {
                      toolbar: {
                        show: true,
                        tools: {
                          download: true,
                        },
                      },
                    },
                  }}
                  series={services?.list.map((item) => item.totalCount ?? 0) || []}
                  type='donut'
                  width='100%'
                />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Result;
