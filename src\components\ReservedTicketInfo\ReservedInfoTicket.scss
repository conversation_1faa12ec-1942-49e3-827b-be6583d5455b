.reserved-info-ticket {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.25rem;
  padding: 0.5rem;
  border-radius: 0.75rem;
  border: 1px solid var(--Secondary-200, #ccf3d2);
  background: var(--color-white);
  overflow: hidden;
}

.reserved-info-ticket::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(transparent, transparent, transparent, #ccf3d2);
  animation: rotate 1s linear infinite;
  z-index: -1;
}

.reserved-info-ticket::after {
  content: "";
  position: absolute;
  inset: 2px;
  background: var(--color-white);
  border-radius: 0.65rem;
  z-index: -1;
}

.reserved-info {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: black !important;
  font-size: 0.875rem;
  font-style: normal;
  font-weight: 600;
  line-height: 1.25rem;
}

.reserved-count {
  color: black !important;
  font-size: 0.875rem;
  font-style: normal;
  font-weight: 400;
  line-height: 1.25rem;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.pulse {
  animation: pulse 1.5s infinite;
}
