import { QueryKeys } from "@/enums/queryKeyEnums";
import { NotificationController } from "@/services/notification-controller/notification";
import { TGetNotifications } from "@/types";
import { CheckOutlined } from "@ant-design/icons";
import { useQueryClient } from "@tanstack/react-query";
import { Badge, Tooltip } from "antd";
import { useMemo } from "react";
import "./NotificationItem.scss";
import GridOver<PERSON>low from "@/components/GridOverflow/GridOverFlow";

const NotificationItem = ({ isRead, id, title, description }: Readonly<TGetNotifications>) => {
  const queryClient = useQueryClient();

  const handleRefresh = async () => {
    await queryClient.invalidateQueries({ queryKey: [QueryKeys.NOTIFICATIONS] });
    await queryClient.invalidateQueries({ queryKey: [QueryKeys.NOTIFICATIONS_COUNT] });
  };

  const status = isRead ? "default" : "processing";
  const titleTooltip = isRead ? "Oxunub" : "Oxundu olaraq işarələ";

  const checkIconStyle = useMemo(
    () => ({
      color: isRead ? "green" : "gray",
      cursor: "pointer",
    }),
    [isRead],
  );

  const handleRead = async () => {
    try {
      await NotificationController.readById(id);
      handleRefresh();
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  return (
    <div className='notification-item'>
      <Badge status={status} className='mr-8' />
      <div className='content-notification-item'>
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <p className='title'>{title.slice(0, 6)}</p>
          <p className='title-date'>{title.slice(6)}</p>
        </div>
        <p className='description'>
          <GridOverFlow className='truncate-text' text={description} />
        </p>
      </div>
      <div style={{ width: "30px", display: "flex", justifyContent: "flex-end" }}>
        <Tooltip title={titleTooltip}>
          <CheckOutlined onClick={handleRead} style={checkIconStyle} />
        </Tooltip>
      </div>
    </div>
  );
};

export default NotificationItem;
