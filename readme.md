# eVisit

A modern React-based web application for visitor management with comprehensive tracking, reporting, and service management capabilities.

## Technical Stack

- **Frontend:** React 18 with TypeScript
- **Build Tool:** Vite
- **State Management:**
  - Zustand
  - TanStack Query (React Query)
- **UI Framework:** Ant Design
- **Styling:** SCSS
- **API Communication:** Axios

## Key Features

- 🔐 Authentication system
- 🔔 Real-time notifications via WebSocket
- 📊 Report generation and downloads
- 🌐 Multi-language support (with Azerbaijani localization)
- 📄 PDF generation for visits
- 📈 Statistics and data visualization using ApexCharts

## Architecture

### Project Structure
```
src/
├── components/     # Reusable UI components
├── config/        # App configurations
├── enums/         # TypeScript enums
├── hooks/         # Custom React hooks
├── layout/        # Layout components
├── services/      # API services
├── store/         # State management
├── styles/        # Global styles
└── types/         # TypeScript type definitions
```

### Core Architecture Components
- Component-based architecture with lazy loading
- Environment-based configuration (development/production)
- Centralized HTTP client with interceptors
- Type-safe API interactions
- Custom hooks for business logic
- Persistent state management

### State Management
- **Zustand** for global state:
  - Authentication state
  - Menu items
  - Active tab state
- **React Query** for server state:
  - Data fetching
  - Cache management
  - Real-time updates

### API Layer
- Centralized HTTP client using Axios
- Interceptors for:
  - Error handling
  - Authentication
  - Request/Response transformation
- Type-safe API interactions

### Component Architecture
- Functional components with TypeScript
- Custom hooks for business logic
- HOCs for protected routes
- Lazy loading for better performance

### Styling Architecture
- SCSS modules
- CSS variables for theming
- Responsive design
- Ant Design customization

### Real-time Features
- WebSocket integration (SockJS + STOMP)
- Real-time notifications system

## Development

### Code Style & Standards
- TypeScript with strict mode
- ESLint for static code analysis
- Prettier for code formatting
- Double quotes for strings
- 2 spaces indentation
- Trailing commas enforced
- Path aliases (`@/*` → `src/*`)

### Quality Assurance
- Pre-commit hooks with Husky
- SonarQube integration
- Automated CI/CD pipeline
- Type safety enforcement

### Environment Setup
```bash
# Development
npm run dev

# Production build
npm run build:prod

# Code formatting
npm run format

# Linting
npm run lint
```

## DevOps

### CI/CD Pipeline
- Automated builds via GitLab CI
- Code quality checks with SonarQube
- Environment-specific deployments
- Automated testing

### Security
- Protected routes implementation
- Authentication state management
- HTTP-only cookies
- Secure WebSocket connections

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables (see `.env.example`)
4. Start development server: `npm run dev`

## Requirements

- Node.js 20+
- npm 9+