import img from "@/assets/images/login-img.webp";
import PasswordIcon from "@/components/icons/PasswordIcon";
import useLogin from "@/hooks/mutationhooks/useLogin";
import { RequestAuthServicePostType } from "@/types";
import { EyeInvisibleOutlined, EyeOutlined, MailOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { useState } from "react";
import "./Login.scss";

const Login = () => {
  const [formData, setFormData] = useState<RequestAuthServicePostType>({
    username: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  const { mutate, isPending } = useLogin();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    mutate(formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev) => ({ ...prev, [e.target.name]: e.target.value }));
  };

  return (
    <div className='login'>
      <div className='main-login'>
        <img
          src={img}
          alt=''
          style={{ width: "100%", height: "100%", objectFit: "cover" }}
          loading='lazy'
        />
        <div className='login-form-container'>
          <form className='login-form' onSubmit={handleSubmit}>
            <div className='upper-button-group'>
              <div className='buttons'>
                <div className='enter'>Giriş</div>
                {/* <Button className='register'>Qeydiyyat</Button> */}
              </div>
            </div>

            <div className='middle-form'>
              <div className='form-input'>
                <div className='label-input'>
                  <MailOutlined />
                </div>
                <input
                  type='text'
                  name='username'
                  placeholder='İstifadəçi'
                  onChange={handleChange}
                  value={formData.username}
                />
              </div>
              <div className='form-input'>
                <div className='label-input'>
                  <PasswordIcon />
                </div>
                <input
                  type={showPassword ? "text" : "password"}
                  name='password'
                  placeholder='Şifrə'
                  onChange={handleChange}
                  value={formData.password}
                />
                <button
                  type='button'
                  className='password-toggle'
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <EyeOutlined className='text-white' />
                  ) : (
                    <EyeInvisibleOutlined className='text-white' />
                  )}
                </button>
              </div>

              {/* <div className='form-input'>
                <div className='label-input'>
                  <PasswordIcon />
                </div>
                <input
                  type='password'
                  name='password'
                  placeholder='Şifrə'
                  onChange={handleChange}
                  value={formData.password}
                />
              </div> */}
            </div>

            {/* <div className='bottom-form'>
              <CustomCheckLogin />
              <Button className='forget' type='link'>
                Şifrəni unutdun
              </Button>
            </div> */}
            <Button loading={isPending} className='approve' htmlType='submit'>
              {isPending ? "Gözləyin" : "Daxil ol"}
            </Button>

            {/* <div className='tools'>
              <p style={{ color: "white" }}>Hesabın yoxdur?</p>
              <Button className='create-account' type='link'>
                Hesab yarat
              </Button>
            </div> */}
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;
