import { TGetNotifications } from "@/types";
import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

type NotificationPopStateSlice = {
  notificationState: boolean;
  setNotificationState: (key: boolean) => void;
  notificationsContent: TGetNotifications[];
  setNotificationsContent: (content: TGetNotifications[]) => void;
};

const createNotificationPopupSlice: StateCreator<
  NotificationPopStateSlice,
  [["zustand/devtools", never]]
> = (set) => ({
  notificationState: false,
  notificationsContent: [],
  setNotificationState: (key) => set({ notificationState: key }, false, `notificationState ${key}`),
  setNotificationsContent(content) {
    set(
      { notificationsContent: content },
      false,
      `pageTypeState notifications-content ${content.toString()}`,
    );
  },
});

export const useNotificationPopStore = create<NotificationPopStateSlice>()(
  devtools(createNotificationPopupSlice),
);
