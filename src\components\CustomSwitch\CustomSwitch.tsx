import React, { useEffect, useState } from "react";
import { Switch } from "antd";
import "./CustomScwitch.scss";
import { useServicesStore } from "@/store/servicesStore";
import { TServiceContent } from "@/types";

const CustomSwitch: React.FC<Readonly<TServiceContent>> = ({ id, price }) => {
  const { serviceIds, setServices, removeService } = useServicesStore();
  const [checked, setChecked] = useState(false);
  useEffect(() => {
    if (serviceIds.length === 0) {
      setChecked(false);
    }
  }, [serviceIds]);

  const onChange = () => {
    if (checked) {
      setChecked(!checked);
      removeService(id);
      return;
    }

    setServices({ id: id, sum: price });
    setChecked(!checked);
  };
  return (
    <div className='custom-switch'>
      <Switch checked={checked} onChange={onChange} />
    </div>
  );
};

export default CustomSwitch;
