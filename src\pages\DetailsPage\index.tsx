import ConfirmationBlock from "@/components/ConfirmationBlock/ConfirmationBlock";
import { Loading } from "@/components/CustomLoading";
import EventDetails from "@/components/EventDetails/EventDetails";
import OtherTable from "@/components/OtherTable/OtherTable";
import MyTable from "@/components/TableComponent/TableComponent";
import { PageType } from "@/enums/page-type";
import PageContainer from "@/hoc/PageContainer/PageContainer";
import TopPage from "@/hoc/TopPage/TopPage";
import useServices from "@/hooks/queryhooks/useServices";
import useVisitCategories from "@/hooks/queryhooks/useVisitCategories";
import { useMenusStore } from "@/store/menuItemsStore";
import { usePageTypeStore } from "@/store/pageTypeStore";
import { useSelectedKeysStore } from "@/store/selectedNavigationStore";
import { useEffect } from "react";
import { useParams } from "react-router-dom";

const DetailsPage = () => {
  const params = useParams();
  const { data, isLoading, isFetching } = useVisitCategories(params?.id);
  const { data: dataServices, isLoading: isLoadingDataServices } = useServices();
  const { pageType } = usePageTypeStore();
  const { menus } = useMenusStore();
  const { setSelectedKeys } = useSelectedKeysStore();
  const { setPageType } = usePageTypeStore();
  const element = menus?.find((item) => item?.id === Number(params?.id));

  useEffect(() => {
    const firstInput = document.querySelector("input");
    firstInput?.focus();
  }, [isFetching]);
  useEffect(() => {
    if (element) {
      setSelectedKeys(element.name);
      setPageType(element.visitType);
    }
  }, [menus, setSelectedKeys, setPageType, element]);
  if (isLoading || isLoadingDataServices) {
    return <Loading />;
  }

  return (
    <PageContainer>
      <TopPage>
        <MyTable datas={data?.content} />
        {pageType === PageType.INDIVIDUAL ? null : <EventDetails />}
        <OtherTable datas={dataServices?.content} />
      </TopPage>
      <ConfirmationBlock />
    </PageContainer>
  );
};

export default DetailsPage;
