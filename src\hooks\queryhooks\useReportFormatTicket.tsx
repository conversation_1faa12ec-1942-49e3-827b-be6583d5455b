import { QueryKeys } from "@/enums/queryKeyEnums";
import { Visit } from "@/services/visit-controller/vistiController";
import { SearchParmasType } from "@/types";
import { useQuery } from "@tanstack/react-query";

const useReportFormatsTicket = ({
  startDate,
  endDate,
  enabled,
}: {
  startDate: SearchParmasType;
  endDate: SearchParmasType;
  enabled: boolean;
}) => {
  return useQuery({
    queryKey: [QueryKeys.REPORT_FORMATS_STATISTIC_TICKET],
    queryFn: () => Visit.getTicketReportFormat(startDate, endDate),
    enabled,
  });
};

export default useReportFormatsTicket;
