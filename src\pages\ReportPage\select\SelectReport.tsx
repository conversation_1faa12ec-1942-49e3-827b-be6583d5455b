import { selectTypes } from "@/constants/selectTypes";
import { usePaginationStore } from "@/store/paginationStore";
import { ReportType, useSearchParamsStore } from "@/store/searchParamsStore";
import { Select, Space } from "antd";
import React from "react";

const SelectReport: React.FC = () => {
  const { reportType, setReportType } = useSearchParamsStore();
  const { setPageSize } = usePaginationStore();
  const handleChange = (value: ReportType) => {
    setReportType(value);
    setPageSize({ page: 1, size: 20 });
  };
  return (
    <Space wrap>
      <Select
        defaultValue={reportType}
        style={{ width: 170, height: "3rem" }}
        onChange={handleChange}
        options={selectTypes}
      />
    </Space>
  );
};

export default SelectReport;
