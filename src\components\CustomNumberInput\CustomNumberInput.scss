.input-wrapper {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  .action-buttons {
    font-size: 1.3rem;
    margin: 0px 0.25rem;
  }
  .custom-input {
    display: flex;
    width: 4.0625rem;
    padding: 0.25rem 0.3rem 0.25rem 0.75rem;
    justify-content: space-between;
    align-items: center;
    border-radius: 0.25rem;
    border: 1px solid var(--Greyscale-200, #e2e8f0);
    &[type="number"]::-webkit-inner-spin-button,
    [type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: inner-spin-button !important;
      appearance: auto !important;
      opacity: 1 !important;
      pointer-events: auto !important;
    }
  }
}
