import { REPORT } from "@/constants/report";
import { ReportType } from "@/store/searchParamsStore";
import dayjs from "dayjs";

export function utilFileName(reportType: ReportType) {
  const now = dayjs();
  const fileHeader = "eVisit_";
  const formattedDate = now.format("YYYY-MM-DD_HH-mm-ss");
  let prefix = "";
  switch (reportType) {
    case REPORT.ticket_finance:
      prefix = "U_";
      break;
    case REPORT.general:
      prefix = "T_";
      break;
    case REPORT.statistics:
      prefix = "Z_";
      break;
    default:
      prefix = "_";
      break;
  }
  return `${fileHeader}${prefix}${formattedDate}.xlsx`;
}
