import { QueryKeys } from "@/enums/queryKeyEnums";
import { ReportByDateController } from "@/services/report-service/report-by-date";
import { SearchParmasType } from "@/types";
import { useQuery } from "@tanstack/react-query";

const useReportByDateGrid = ({
  startDate,
  endDate,
  page,
  size,
  enabled,
}: {
  startDate: SearchParmasType;
  endDate: SearchParmasType;
  page?: number;
  size?: number;
  enabled: boolean;
}) =>
  useQuery({
    queryKey: [QueryKeys.REPORT_GRID_BY_DATE, page, size],
    queryFn: () => ReportByDateController.getReportGrid(startDate, endDate, page, size),
    enabled,
  });

export default useReportByDateGrid;
