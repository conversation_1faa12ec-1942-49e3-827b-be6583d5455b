import { ResponseAuthServicePostType } from "@/types";
import { TUserSlice } from "@/types/store-types/auth-store-types";
import { create, StateCreator } from "zustand";
import { devtools, persist } from "zustand/middleware";

const createUserSlice: StateCreator<
  TUserSlice,
  [["zustand/devtools", never], ["zustand/persist", unknown]]
> = (set) => ({
  user: {
    name: "",
    surname: "",
    userId: null,
  },
  setUser: (user) => {
    set({ user });
  },
  clearUser: () => {
    set({ user: { name: null, surname: null, userId: null } }, false, "clearUser");
    customStorage.removeItem("user-storage");
  },
});
const customStorage = {
  getItem: (name: string) => {
    const item = localStorage.getItem(name);
    return item ? JSON.parse(item) : null;
  },
  setItem: (name: string, value: unknown) => {
    localStorage.setItem(name, JSON.stringify(value));
  },
  removeItem: (name: string) => {
    localStorage.removeItem(name);
  },
};

export const useAuthStore = create<TUserSlice>()(
  devtools(
    persist(createUserSlice, {
      name: "user-storage",
      storage: customStorage,
    }),
  ),
);

export const setTokenDirectly = (user: ResponseAuthServicePostType) => {
  useAuthStore.getState().setUser(user);
};

export const getToken = () => useAuthStore.getState().user;
