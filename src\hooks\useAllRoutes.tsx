import { RoutePaths } from "@/enums/rootEnums";
import { DefaultPage, DetailsPage, ReportPage } from "@/pages";
const useAllRoutes = () => {
  return [
    {
      path: RoutePaths.DEFAULT,
      element: <DefaultPage />,
    },
    {
      path: RoutePaths.DEFAULT_WITH,
      element: <DetailsPage />,
    },
    {
      path: RoutePaths.REPORT,
      element: <ReportPage />,
    },
    {
      path: RoutePaths.ERROR,
      element: <>Error Page</>,
    },
  ];
};

export default useAllRoutes;
