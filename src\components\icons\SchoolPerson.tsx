import { colors } from "@/constants/colors";
import { IconProps } from "@/types/iconTypes/icon-types";
import { FC } from "react";

const SchoolPerson: FC<IconProps> = ({ active }) => {
  const colorIcon = active ? colors.main_green : colors.white;
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='36' height='36' viewBox='0 0 36 36' fill='none'>
      <path
        d='M7.97024 17.2965H0.744086L5.66245 13.8263H11.2514L7.97024 17.2965ZM28.0301 17.2965H35.2563L30.3379 13.8263H24.7488L28.0301 17.2965ZM14.1151 36H17.2566V25.6581H14.1151V36ZM18.4179 36H21.5594V25.6581H18.4179V36ZM35.8734 18.4577V36H22.7207V25.0774V24.4968H22.1401H18.4179H17.2566H13.5344H12.9538V25.0774V36H0.126953V18.4577H8.47045L13.2353 13.4184L17.4195 8.99325V0H18.5808H24.9473L24.6482 0.194766L21.2876 2.3831L24.6482 4.57151L24.9473 4.7662H18.5808V8.99318L22.765 13.4184L27.5299 18.4577H35.8734V18.4577ZM5.68748 30.2408H2.37766V33.5507H5.68748V30.2408ZM5.68748 25.0774H2.37766V28.3873H5.68748V25.0774ZM10.9833 30.2408H7.67345V33.5507H10.9833V30.2408ZM10.9833 25.0774H7.67345V28.3873H10.9833V25.0774ZM20.7142 18.8549C20.7142 17.3559 19.4991 16.1408 18.0002 16.1408C16.5013 16.1408 15.2861 17.3559 15.2861 18.8549C15.2861 20.3538 16.5013 21.569 18.0002 21.569C19.4991 21.569 20.7142 20.3539 20.7142 18.8549ZM28.3269 30.2408H25.0171V33.5507H28.3269V30.2408ZM28.3269 25.0774H25.0171V28.3873H28.3269V25.0774ZM33.6227 30.2408H30.3129V33.5507H33.6227V30.2408ZM33.6227 25.0774H30.3129V28.3873H33.6227V25.0774Z'
        fill={colorIcon}
      />
    </svg>
  );
};

export default SchoolPerson;
