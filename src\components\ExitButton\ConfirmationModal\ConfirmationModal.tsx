import { Button, Modal } from "antd";
import { FC } from "react";
import "./ConfirmationModal.scss";

const ConfirmationModal: FC<{
  isModalOpen: boolean;
  handleCancel: () => void;
  handleOk: () => void;
}> = ({ isModalOpen, handleCancel, handleOk }) => {
  return (
    <Modal
      centered
      className='custom-modal'
      open={isModalOpen}
      onCancel={handleCancel}
      footer={null}
    >
      <div className='content'>
        <h1>Hörmətli istifadəçi</h1>
        <p>Hesabdan çıxış etməyə əminsiniz?</p>
      </div>
      <div className='button-group'>
        <Button className='cancel-btn' onClick={handleCancel}>
          Xeyr
        </Button>
        <Button className='confirm-btn' onClick={handleOk}>
          Bəli
        </Button>
      </div>
    </Modal>
  );
};

export default ConfirmationModal;
