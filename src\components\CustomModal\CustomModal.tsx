import { Modal } from "antd";
import { FC, ReactNode } from "react";
const CustomModal: FC<{
  isModalOpen: boolean;
  handleClose: () => void;
  children: ReactNode;
}> = ({ isModalOpen, children, handleClose }) => {
  return (
    <Modal
      centered
      className='custom-modal'
      open={isModalOpen}
      onCancel={handleClose}
      footer={null}
      closable={false}
    >
      {children}
    </Modal>
  );
};

export default CustomModal;
