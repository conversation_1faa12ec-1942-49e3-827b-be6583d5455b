import { NullableDayjs } from "@/types/util-types";

export const returnCombinedDateAsIsoString = ({
  date,
  time,
}: {
  date: NullableDayjs;
  time: NullableDayjs;
}) => {
  if (date && time) {
    const combinedStart = date
      .hour(time.hour())
      .minute(time.minute())
      .second(time.second())
      .millisecond(time.millisecond());

    return combinedStart.format("YYYY-MM-DDTHH:mm:ss");
  }
  return null;
};

export const formatValue = (input: string): string => {
  let digits = input.replace(/\D/g, "");

  if (!digits.startsWith("994")) {
    digits = "994" + digits.replace(/^994/, "");
  }
  const part1 = digits.slice(3, 5);
  const part2 = digits.slice(5, 8);
  const part3 = digits.slice(8, 10);
  const part4 = digits.slice(10, 12);
  let formatted = "+994 ";
  if (part1) formatted += part1;
  if (part2) formatted += " " + part2;
  if (part3) formatted += "  " + part3;
  if (part4) formatted += "  " + part4;

  return formatted;
};
