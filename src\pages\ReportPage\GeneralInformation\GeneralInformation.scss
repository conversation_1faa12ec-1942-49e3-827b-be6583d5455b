.table-wrapper {
  flex-direction: column;
  align-items: flex-start;
  overflow: hidden;
  border-radius: 0.75rem;
  border: 1px solid var(--Greyscale-200);

  .custom-table {
    .ant-table-thead > tr > th {
      text-align: center;
      white-space: normal;
      word-wrap: break-word;
      line-height: 1.5;
      padding: 8px;
      height: auto;
      font-size: 12px;
      font-weight: bold;
    }

    .ant-table-thead > tr > th::before {
      display: none !important;
    }

    .ant-table-tbody > tr > td {
      text-align: center;
    }
  }

  .ant-table-tbody > tr > td {
    padding: 0.5rem 0.5rem !important;
  }

  .ant-table-wrapper .ant-table-thead > tr > th {
    padding: 0.5rem 0.5rem !important;
  }
}
