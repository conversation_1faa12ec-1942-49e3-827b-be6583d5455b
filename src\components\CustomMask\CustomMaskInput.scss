.input-wrap {
  width: 100%;
  padding: 15px 10px 5px 15px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 0.25rem;
  outline: none;
  transition: all 0.2s ease;
  gap: var(--Distance-4x2, 0.5rem);

  &:focus {
    border-color: var(--Greyscale-200);
  }

  &:not(:placeholder-shown) + .input-label {
    top: 0.5rem;
    font-size: 12px;
    color: var(--Greyscale-900);
  }
}

.input-container {
  position: relative;
  width: 100%;

  &.focused .input-label {
    top: 0.5rem;
    left: 1rem;
    font-size: 12px;
    color: var(--Greyscale-400);
  }
}

.input-label {
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
  color: #aaa;
  font-size: 1rem;
  transition: all 0.2s ease;
  pointer-events: none;
}
