import { Loading } from "@/components/CustomLoading";
import CustomNavbarButton from "@/components/CustomNavbarButton/CustomNavbarButton";

import useNavigations from "@/hooks/queryhooks/useNavigations";
import { useMenuItems } from "@/hooks/useMenuItems";
import SearchInputs from "@/pages/ReportPage/SearchInputs/SearchInputs";
import { FileService } from "@/services/file-controller/fileService";
import { useActiveTabKeyStore } from "@/store/activeTabkey";
import { useMenusStore } from "@/store/menuItemsStore";
import { useEffect } from "react";
import defaultActive from "../../../../assets/default_images/userblack.png";
import defaultDeactive from "../../../../assets/default_images/deaktive.png";

const Navbar = () => {
  const { data, isLoading } = useNavigations();
  const { setMenus } = useMenusStore();

  const menusItems = useMenuItems();
  const { activeKey } = useActiveTabKeyStore();

  useEffect(() => {
    const fetchImageUrls = async () => {
      if (data?.content) {
        const transformed = await Promise.all(
          data.content.map(async (item) => {
            const activePhotoUrl =
              item.activePhoto && item
                ? await FileService.getSvgUrl({ id: item.activePhoto.uuid })
                : defaultActive;
            const deActivePhotoUrl =
              item.deActivePhoto && item
                ? await FileService.getSvgUrl({ id: item.deActivePhoto.uuid })
                : defaultDeactive;
            return {
              ...item,
              activePhotoUrl,
              deActivePhotoUrl,
            };
          }),
        );

        setMenus(transformed);
      }
    };

    fetchImageUrls();
  }, [data?.content, setMenus]);
  if (isLoading) {
    return <Loading />;
  }
  return (
    <div className='head-bottom'>
      {activeKey === "2" ? (
        <SearchInputs />
      ) : (
        menusItems.map((item) => <CustomNavbarButton {...item} key={item.keyName} />)
      )}
    </div>
  );
};

export default Navbar;
