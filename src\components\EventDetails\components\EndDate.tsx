import { useEventStore } from "@/store/eventStore";
import { dateTyppe } from "@/types";
import { DatePicker, TimePicker } from "antd";
import React from "react";

const EndDate: React.FC = () => {
  const { endDate, endTime, setEndDate, setEndTime } = useEventStore();

  const handleDateChange = (date: dateTyppe) => {
    setEndDate(date);
  };

  const handleTimeChange = (time: dateTyppe) => {
    setEndTime(time);
  };

  return (
    <div className='event-input start-date'>
      <p>Bitmə tarixi</p>
      <div>
        <div className='picker-wrapper-all'>
          <DatePicker
            style={{ width: "100%" }}
            value={endDate}
            onChange={handleDateChange}
            placeholder='Gün.Ay.İl'
            format={"DD.MM.YYYY"}
          />
          <TimePicker
            style={{ width: "82%" }}
            value={endTime}
            onChange={handleTimeChange}
            format='HH:mm'
            placeholder='Saat:Dəqiqə'
          />
        </div>
      </div>
    </div>
  );
};

export default EndDate;
