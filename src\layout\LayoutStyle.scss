.main-layout {
  z-index: 2;
  height: 100vh;
  position: absolute;
  width: 100%;
  background-color: transparent;
  .ant-layout-header {
    height: auto;
  }

  .header {
    background-color: transparent;
    flex: 1;
    padding: 0;
    justify-content: center;
    display: flex;

    .head-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      padding: 0 50px;
      .head-top {
        width: 100%;
        max-width: 1800px;
        height: 50px;
        padding: 0 4px;
        border-bottom: 1px solid var(--color-gray-light);
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          .logo {
            display: flex;
            width: 21.375rem;
            height: 2.75rem;
            align-items: center;
            gap: 0.4375rem;
            margin-right: 3.7rem;
          }
        }
        .right {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          .user-name-title {
            background-color: transparent;
            border: none;
            cursor: pointer;
          }
          .notification {
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            color: var(--color-white);
            position: relative;
            cursor: pointer;
          }
          .profile {
            cursor: pointer;
            display: flex;
            width: 2.25rem;
            height: 2.25rem;
            padding: 0.61369rem 0.625rem 0.63631rem 0.625rem;
            justify-content: center;
            align-items: center;
            border-radius: 6.1875rem;
            background: var(--color-white);
          }
          .dropdown-title {
            color: var(--color-white);
          }
          .title {
            color: var(--color-white);
            font-family:
              "Public Sans" sans-serif "Lucida Sans",
              "Lucida Sans Regular",
              "Lucida Grande",
              "Lucida Sans Unicode",
              Geneva,
              Verdana,
              sans-serif;
            font-size: 0.9375rem;
            font-style: normal;
            font-weight: 500;
            line-height: 1.25rem;
          }
        }
      }
      .head-bottom-container {
        padding: 0 50px;
        margin: 0.5rem 0;
        display: flex;
        justify-content: center;
        .head-bottom {
          display: flex;
          padding: 0.5rem;
          flex-wrap: wrap;
          gap: 1rem;
          align-self: stretch;
          border-radius: 1rem;
          background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.2) 99.92%),
            rgba(255, 255, 255, 0.12);
          backdrop-filter: blur(6px);
          .head-nav-item {
            max-width: 100px;
            min-width: 100px;
            display: flex;
            overflow: hidden;
            height: 3rem;
            justify-content: center;
            align-items: center;
            gap: 0.625rem;
            flex: 1 100 0;
            border-radius: 0.75rem;
            border: 1px solid var(--color-gray-light);
          }
        }
      }
    }
  }
  .content {
    background-color: transparent;
    flex: 8;
    height: 100%;
    display: flex;
    align-items: stretch;

    .content-container {
      padding: 0 50px;
      min-height: 100%;
      width: 100%;
      display: flex;
      justify-content: center;
      .content-main {
        width: 100%;
        max-width: 1800px;
        display: flex;
        padding: 0.625rem;
        gap: 1rem;
        align-items: stretch;
        border-radius: 1rem;
        background: var(--color-white);
      }
    }
  }
  .footer {
    display: flex;
    justify-content: center;
    flex: 0.2;
    background-color: transparent;
    width: 100%;
    .footer-inner {
      display: flex;
      width: 100%;
      max-width: 1800px;
      justify-content: space-between;
    }
  }
}
