import { Auth } from "@/services/auth-controller/auth";
import { useAuthStore } from "@/store/authStore";
import { RequestAuthServicePostType } from "@/types";

import { useMutation } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";

const useLogin = () => {
  const navigate = useNavigate();
  const { setUser } = useAuthStore();
  return useMutation({
    mutationFn: (postData: RequestAuthServicePostType) => Auth.post(postData),
    onSuccess: (data) => {
      setUser(data);
      return navigate("/");
    },
  });
};

export default useLogin;
