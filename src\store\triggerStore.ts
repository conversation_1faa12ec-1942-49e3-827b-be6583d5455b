import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

type TriggerSlice = {
  trigger: boolean;
  setTrigger: (key: boolean) => void;
};

const createTriggerSlice: StateCreator<TriggerSlice, [["zustand/devtools", never]]> = (set) => ({
  trigger: false,
  setTrigger: (key) => set({ trigger: key }, false, `setTriggerState ${key}`),
});

export const useTriggerStore = create<TriggerSlice>()(devtools(createTriggerSlice));
