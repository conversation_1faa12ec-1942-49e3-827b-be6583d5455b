.pop-notification {
  position: absolute;
  z-index: 3;
  top: 3.2rem;
  left: -270px;
  height: auto;
  max-height: 32.75rem;
  overflow-y: auto;
  overflow-x: none;
  display: flex;
  width: 25.25rem;
  padding: 1rem 1.5rem 1rem 1rem;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
  border-radius: 1.25rem;
  background: var(--White, #fff);
  box-shadow:
    0px 3px 6px -4px rgba(23, 25, 35, 0.12),
    0px 6px 16px 0px rgba(23, 25, 35, 0.08),
    0px 9px 28px 8px rgba(23, 25, 35, 0.05);

  .notification-wrapper {
    overflow-y: auto;
    width: 100%;
    padding-right: 4px;
  }
}

.modal-not {
  max-height: 300px;
  overflow-y: auto;
}
// Button SCSS
.custom-read-button {
  // Base styles
  padding: 4px 8px;
  background-color: #2c7a7b;
  outline: none;
  border: none;
  color: white;
  cursor: pointer;
  // border-radius: 4px;
  margin-left: 5px;
  &:hover {
    background-color: #2c7a7b !important ;
    color: white !important;
  }
}
