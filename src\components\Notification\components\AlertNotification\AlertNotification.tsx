import { QueryKeys } from "@/enums/queryKeyEnums";
import { NotificationController } from "@/services/notification-controller/notification";
import { TGetNotifications } from "@/types";
import { CheckOutlined } from "@ant-design/icons";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "antd";

const AlertNotification = ({ id, title, description }: Readonly<TGetNotifications>) => {
  const queryClient = useQueryClient();
  const handleRefresh = async () => {
    await queryClient.invalidateQueries({ queryKey: [QueryKeys.NOTIFICATIONS] });
    await queryClient.invalidateQueries({ queryKey: [QueryKeys.NOTIFICATIONS_COUNT] });
  };
  const handleRead = async () => {
    try {
      await NotificationController.readById(id);
      handleRefresh();
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };
  return (
    <div key={id} style={{ marginBottom: "10px" }}>
      <p style={{ textAlign: "justify" }}>
        <strong style={{ fontSize: "1.1rem", color: "red" }}>{title} : </strong>
        {description}
      </p>
      <div style={{ display: "flex", justifyContent: "flex-end" }}>
        <Button
          type='primary'
          className='custom-read-button'
          icon={<CheckOutlined />}
          onClick={handleRead}
        >
          Məlumatla tanış oldum
        </Button>
      </div>
    </div>
  );
};

export default AlertNotification;
