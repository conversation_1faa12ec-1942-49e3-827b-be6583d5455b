import { ActiveTabType } from "@/types/store-types/active-tab-types";
import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

const createActiveKeySlice: StateCreator<ActiveTabType, [["zustand/devtools", never]]> = (set) => ({
  activeKey: "1",
  setActiveKey: (key) => set({ activeKey: key }, false, `activekeyState ${key}`),
  clearActiveKey: () => set({ activeKey: "" }, false, `activekeyState ${"clearing active"}`),
});

export const useActiveTabKeyStore = create<ActiveTabType>()(devtools(createActiveKeySlice));
