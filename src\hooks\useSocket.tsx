import { useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";

export const useWebSocket = ({
  userId,
  refetch,
}: {
  userId: number | null;
  refetch: () => void;
}) => {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!userId) {
      console.error("User ID is null, skipping WebSocket connection.");
      return;
    }

    if (window.SockJS && window.Stomp) {
      const socket = new window.SockJS(`https://evisit.aist.group/api/ws`);
      const stompClient = window.Stomp.over(socket);
      stompClient.debug = () => {}; // Disable STOMP logs (optional)

      const onConnect = () => {
        console.log("Connected to WebSocket");

        stompClient.subscribe(`/topic-user/${userId}/notification`, (data: { body: string }) => {
          console.log("Raw message received:", data);
          try {
            console.log("Invalidating queries...");
            const parsedData = JSON.parse(data.body);
            console.log("✅ Parsed notification:", parsedData);
            console.log("🔄 Forcing query refetch...");
            refetch();
          } catch (error) {
            console.error("Error parsing notification data", error);
          }
        });
      };

      const onError = (error: unknown) => {
        console.error("WebSocket connection error:", error);
      };

      stompClient.connect({}, onConnect, onError);
      stompClient.debug = (msg: string) => console.log("[STOMP DEBUG]", msg);

      return () => {
        if (stompClient?.connected) {
          stompClient.disconnect(() => {
            console.log("Disconnected from WebSocket");
          });
        }
      };
    } else {
      console.error("SockJS or StompJS is not available.");
    }
  }, [userId, queryClient, refetch]);

  return null;
};
