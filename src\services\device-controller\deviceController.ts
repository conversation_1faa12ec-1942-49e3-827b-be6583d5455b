import { Endpoints } from "@/enums/endPointEnums";
import { TRequestDevice, TResponseDevice, TResponseDeviceController } from "@/types";
import { AxiosResponse } from "axios";
import { httpClient } from "../httpclient";

export class DeviceController {
  static async get(): Promise<TResponseDeviceController> {
    const response: AxiosResponse<TResponseDeviceController> = await httpClient.get(
      `${Endpoints.DEVICE}`,
    );
    return response.data;
  }
  static async put(id: number | string, postBody: TRequestDevice): Promise<TResponseDevice> {
    const response: AxiosResponse<TResponseDevice> = await httpClient.put(
      `${Endpoints.DEVICE}/${id}`,
      postBody,
    );
    return response.data;
  }
  static async post(postData: TRequestDevice): Promise<TResponseDevice> {
    const response: AxiosResponse<TResponseDevice> = await httpClient.post(
      `${Endpoints.DEVICE}`,
      postData,
    );
    return response.data;
  }
}
