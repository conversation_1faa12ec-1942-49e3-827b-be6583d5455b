import { QueryKeys } from "@/enums/queryKeyEnums";
import { Visit } from "@/services/visit-controller/vistiController";
import { SearchParmasType } from "@/types";
import { useQuery } from "@tanstack/react-query";

const useReportCategoriesTicket = ({
  startDate,
  endDate,
  enabled,
}: {
  startDate: SearchParmasType;
  endDate: SearchParmasType;
  enabled: boolean;
}) => {
  return useQuery({
    queryKey: [QueryKeys.REPORT_CATEGORIES_STATISTIC_TICKET],
    queryFn: () => Visit.getTicketReportCategories(startDate, endDate),
    enabled,
  });
};
export default useReportCategoriesTicket;
