import { ReportResponseType } from "@/types";
import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

type InitialTYpe = ReportResponseType | null;
type InformationSlice = {
  formats: InitialTYpe;
  categories: InitialTYpe;
  services: InitialTYpe;
  setFormats: (key: InitialTYpe) => void;
  setCategories: (key: InitialTYpe) => void;
  setServices: (key: InitialTYpe) => void;
};

const createInformationSlice: StateCreator<InformationSlice, [["zustand/devtools", never]]> = (
  set,
) => ({
  formats: null,
  categories: null,
  services: null,
  setFormats: (key) => set({ formats: key }, false, `menuSetStatesetformats ${key}`),
  setCategories: (key) => set({ categories: key }, false, `menuSetStatesetCategories ${key}`),
  setServices: (key) => set({ services: key }, false, `menuSetStatesetServices ${key}`),
});

export const useInformationStore = create<InformationSlice>()(devtools(createInformationSlice));
