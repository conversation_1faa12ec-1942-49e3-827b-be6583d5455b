import { Endpoints } from "@/enums/endPointEnums";
import { AxiosResponse } from "axios";
import { httpClient } from "../httpclient";
import { RequestAuthServicePostType, ResponseAuthServicePostType } from "@/types";

export class Auth {
  static async post(postData: RequestAuthServicePostType): Promise<ResponseAuthServicePostType> {
    const response: AxiosResponse<ResponseAuthServicePostType> = await httpClient.post(
      `${Endpoints.AUTH}`,
      postData,
    );
    return response.data;
  }
  static async logout() {
    const response = await httpClient.post(`${Endpoints.AUTH_LOGOUT}`);
    return response.data;
  }
}
