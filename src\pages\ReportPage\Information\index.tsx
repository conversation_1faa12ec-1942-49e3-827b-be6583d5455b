import { useInformationStore } from "@/store/informationStore";
import { useSearchParamsStore } from "@/store/searchParamsStore";
import React from "react";
import "./Information.scss";
import { RenderComponent } from "./const";

const DashboardStats: React.FC = () => {
  const { categories, formats, services } = useInformationStore();
  const { reportType } = useSearchParamsStore();
  const notData = !categories || !formats || !services;

  if (notData) {
    return null;
  }
  return RenderComponent[reportType] || null;
};

export default DashboardStats;
