import { useEventStore } from "@/store/eventStore";
import { Input } from "antd";

const EventName = () => {
  const { eventName, setEventName } = useEventStore();
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEventName(e.target.value);
  };
  return (
    <div className='event-input name'>
      <p>Tədbirin adı *</p>
      <Input
        value={eventName}
        className='name-input'
        onChange={handleChange}
        placeholder='Daxil edin'
      />
    </div>
  );
};

export default EventName;
