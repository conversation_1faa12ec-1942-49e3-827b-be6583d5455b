import PageContainer from "@/hoc/PageContainer/PageContainer";
import { useMenusStore } from "@/store/menuItemsStore";
import { usePageTypeStore } from "@/store/pageTypeStore";
import { useSelectedKeysStore } from "@/store/selectedNavigationStore";
import { Card, Skeleton } from "antd";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

const DefaultPage = () => {
  const { menus } = useMenusStore();
  const { setSelectedKeys } = useSelectedKeysStore();
  const { setPageType } = usePageTypeStore();
  const navigate = useNavigate();
  useEffect(() => {
    if (menus.length > 0) {
      setSelectedKeys(menus[0].name);
      navigate(menus[0].id.toString());
      setPageType(menus[0].visitType);
    }
  }, [menus, navigate, setSelectedKeys, setPageType]);
  return (
    <PageContainer>
      <div style={{ padding: 24 }}>
        <Card style={{ marginBottom: 16 }}>
          <Skeleton active paragraph={{ rows: 2 }} />
        </Card>
        <Card style={{ marginBottom: 16 }}>
          <Skeleton active paragraph={{ rows: 3 }} />
        </Card>
        <Card style={{ marginBottom: 16 }}>
          <Skeleton active paragraph={{ rows: 2 }} />
        </Card>
      </div>
    </PageContainer>
  );
};

export default DefaultPage;
