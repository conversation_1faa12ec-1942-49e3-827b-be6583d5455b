import useNotificationsCountQuery from "@/hooks/queryhooks/useNotificationCountQuery";
import useNotificationsQuery from "@/hooks/queryhooks/useNotifications";
import { useGlobalEvents } from "@/hooks/useGlobalEvents";
import { useWebSocket } from "@/hooks/useSocket";
import { useNotificationPopStore } from "@/store/notificationStore";
import { ResponseAuthServicePostType } from "@/types";
import { BellOutlined, WarningOutlined } from "@ant-design/icons";
import { Badge } from "antd";
import { useRef } from "react";
import CustomModal from "../CustomModal/CustomModal";
import "./Notification.scss";
import AlertNotification from "./components/AlertNotification/AlertNotification";
import NotificationItem from "./components/NotificationItem/NotificationItem";
import TitleNotification from "./components/TitleNotification/TitleNotification";

const Notification: React.FC<{ user: ResponseAuthServicePostType }> = ({ user }) => {
  const { notificationState } = useNotificationPopStore();

  const { data: dataNotification, refetch: notificationRefetch } = useNotificationsQuery();
  const { data: notificationCount, refetch: countRefetch } = useNotificationsCountQuery();
  const refetching = async () => {
    await notificationRefetch();
    await countRefetch();
  };
  useWebSocket({ userId: user.userId, refetch: refetching });
  const popupRef = useRef(null);
  useGlobalEvents(popupRef);
  const isWaitingToBeRead = dataNotification?.content.some(
    (item) => item.notificationPriority === "high" && !item.isRead,
  );

  return (
    <>
      <CustomModal isModalOpen={!!isWaitingToBeRead} handleClose={() => {}}>
        <div className='modal-not'>
          <div className='notification-wrapper'>
            <div style={{ display: "flex", justifyContent: "center" }}>
              <WarningOutlined style={{ color: "red", fontSize: "2rem" }} />
            </div>

            {dataNotification?.content
              .filter((item) => !item.isRead && item.notificationPriority === "high")
              .map((content) => {
                return <AlertNotification key={content.id} {...content} />;
              })}
          </div>
        </div>
      </CustomModal>
      {notificationState ? (
        <div className='pop-notification' ref={popupRef}>
          <TitleNotification />
          <div className='notification-wrapper'>
            {dataNotification?.content.length === 0 ? (
              <span style={{ color: "gray" }}>Məlumat yoxdur</span>
            ) : (
              <>
                {dataNotification?.content.map((content) => {
                  return <NotificationItem key={content.id} {...content} />;
                })}
              </>
            )}
          </div>
        </div>
      ) : null}
      <Badge dot={!!notificationCount && notificationCount > 0}>
        <BellOutlined style={{ fontSize: "1.5rem", color: "white" }} />
      </Badge>
    </>
  );
};

export default Notification;

// const mockNotificationData = [
//   {
//   id: 1,
//   title: "title",
//   description: "description ergwerg wergwerg wergwerg wergwerg wergwerg wergwerg wergwerg wergwergw ergwergw ergwergwerg",
//   isRead: false,
//   notificationPriority: "high",
// },
//   {
//   id: 2,
//   title: "title",
//   description: "description ergwerg wergwerg wergwerg wergwerg wergwerg wergwerg wergwerg wergwergw ergwergw ergwergwerg",
//   isRead: false,
//   notificationPriority: "high",
// },
//   {
//   id: 4,
//   title: "title",
//   description: "description ergwerg wergwerg wergwerg wergwerg wergwerg wergwerg wergwerg wergwergw ergwergw ergwergwerg",
//   isRead: false,
//   notificationPriority: "high",
// },
//   {
//   id: 3,
//   title: "title",
//   description: "description ergwerg wergwerg wergwerg wergwerg wergwerg wergwerg wergwerg wergwergw ergwergw ergwergwerg",
//   isRead: false,
//   notificationPriority: "high",
// },
//   {
//   id: 3,
//   title: "title",
//   description: "description ergwerg wergwerg wergwerg wergwerg wergwerg wergwerg wergwerg wergwergw ergwergw ergwergwerg",
//   isRead: false,
//   notificationPriority: "high",
// },
// ]
