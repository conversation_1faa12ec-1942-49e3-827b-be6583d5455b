import { Loading } from "@/components/CustomLoading";
import CustomLayout from "@/layout/Layout";

import ProtectedRoute from "@/routes/protectedRoute";
import { Suspense } from "react";
import { Outlet } from "react-router-dom";

const ProtectedLayoutWithOutlet = () => {
  return (
    <ProtectedRoute>
      <CustomLayout>
        <Suspense fallback={<Loading />}>
          <Outlet />
        </Suspense>
      </CustomLayout>
    </ProtectedRoute>
  );
};
export default ProtectedLayoutWithOutlet;
