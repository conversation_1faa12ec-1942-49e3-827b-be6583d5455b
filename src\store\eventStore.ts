import { NullableDayjs } from "@/types/util-types";
import dayjs from "dayjs";
import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

type EventSliceType = {
  eventName: string;
  startDate: NullableDayjs;
  startTime: NullableDayjs;
  endDate: NullableDayjs;
  endTime: NullableDayjs;
  setEventName: (key: string) => void;
  setStartDate: (key: NullableDayjs) => void;
  setStartTime: (key: NullableDayjs) => void;
  setEndDate: (key: NullableDayjs) => void;
  setEndTime: (key: NullableDayjs) => void;
  clear: () => void;
};

const createEventSlice: StateCreator<EventSliceType, [["zustand/devtools", never]]> = (set) => ({
  eventName: "",
  startDate: dayjs(),
  startTime: dayjs().startOf("minute"),
  endDate: dayjs(),
  endTime: dayjs().set("hour", 18).set("minute", 0).set("second", 0).set("millisecond", 0),
  setEventName: (key) => set({ eventName: key }, false, `EventState ${key}`),
  clear: () =>
    set(
      {
        eventName: "",
        startDate: dayjs(),
        startTime: dayjs().startOf("minute"),
        endDate: dayjs(),
        endTime: dayjs().set("hour", 18).set("minute", 0).set("second", 0).set("millisecond", 0),
      },
      false,
      `EventStateClear ${"clearing"}`,
    ),
  setStartDate: (key) => set({ startDate: key }, false, `StartDate`),
  setStartTime: (key) => set({ startTime: key }, false, `StartTime`),
  setEndDate: (key) => set({ endDate: key }, false, `EndDate`),
  setEndTime: (key) => set({ endTime: key }, false, `EndTime`),
});

export const useEventStore = create<EventSliceType>()(devtools(createEventSlice));
