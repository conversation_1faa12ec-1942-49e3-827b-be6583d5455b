import TruncateTooltip from "@/components/TruncatedTooltip/TruncatedTooltip";
import { REPORT } from "@/constants/report";
import { useInformationStore } from "@/store/informationStore";
import { useSearchParamsStore } from "@/store/searchParamsStore";
import { formatNumber, formatThousand } from "@/utils/formatNumber";
import Chart from "react-apexcharts";
import { getTitleUtil } from "./utils";
import ReservedInfoTicket from "@/components/ReservedTicketInfo/ReservedInfoTicket";
const FinanceStatisticTicket = () => {
  const { reportType } = useSearchParamsStore();
  const { categories, formats, services } = useInformationStore();
  const isLoading = !categories || !formats || !services;

  if (isLoading) {
    return "";
  }
  const isFinance = reportType === REPORT.finance;
  const isStatistic = reportType === REPORT.statistics;
  const { categoryTitle, formatTitle, serviceTitle } = getTitleUtil({ isStatistic, isFinance });
  const totalFinance = isFinance ? formats?.total + services?.total : formats?.total;
  const symbolAzn = isFinance ? "₼" : "";
  return (
    <div className='dashboard-stats'>
      <div className='top-info'>
        <h2>
          <div>
            {formatTitle}:
            <span>
              {` `}{" "}
              {isFinance
                ? formatNumber(totalFinance ?? 0)
                : totalFinance
                  ? formatThousand(totalFinance)
                  : 0}{" "}
              {symbolAzn}
            </span>
          </div>
          <ReservedInfoTicket />
        </h2>

        <div className='summary-cards'>
          {formats?.list.map((item) => (
            <div className='summary-card' key={item.name + item.count}>
              <div className='tool-wrap'>
                <span>
                  {isFinance
                    ? formatNumber(item.count?.toString())
                    : item.count
                      ? formatThousand(item.count)
                      : 0}{" "}
                  {symbolAzn}
                </span>
                <span className='index'>({((item.count / formats.total) * 100).toFixed(2)}%)</span>
              </div>
              <TruncateTooltip text={item.name} />
            </div>
          ))}
        </div>
      </div>
      {totalFinance === 0 ? (
        <></>
      ) : (
        <div className='charts-section'>
          <div className='chart-container'>
            <h3>{categoryTitle}</h3>
            <div className='chart-wrapper'>
              <div className='chart-legend'>
                <p className='chart-total'>
                  Ümumi:
                  {isFinance
                    ? ` ${formatNumber(categories?.total?.toString())} `
                    : ` ${categories?.total ? formatThousand(categories.total) : 0} `}
                  {symbolAzn}
                </p>
              </div>

              <Chart
                options={{
                  labels: categories?.list?.map(
                    (item) =>
                      `${item.name} : ${item.count ? formatThousand(item.count?.toString()) : 0} -bilet`,
                  ) || ["No Data"],
                  legend: {
                    show: true,
                    position: "right",
                    horizontalAlign: "left",
                    fontSize: "13px",
                    width: 400,
                    markers: {
                      strokeWidth: 4,
                      shape: "circle",
                    },
                  },
                  dataLabels: {
                    enabled: true,
                    style: {
                      fontSize: "13px",
                      colors: ["#000"],
                    },
                    dropShadow: {
                      enabled: false,
                    },
                  },
                  tooltip: {
                    y: {
                      formatter: () => ``,
                    },
                  },
                  plotOptions: {
                    pie: {
                      customScale: 0.9,
                      donut: {
                        size: "35%",
                      },
                    },
                  },
                  chart: {
                    toolbar: {
                      show: true,
                      tools: {
                        download: true,
                      },
                    },
                  },
                }}
                series={categories?.list?.map((item) => item.count ?? 0) || [0]}
                type='donut'
                width='100%'
              />
            </div>
          </div>

          <div className='chart-container'>
            <h3>{serviceTitle}</h3>
            <div className='chart-legend'>
              <p className='chart-total'>
                Ümumi:
                {isFinance
                  ? ` ${formatNumber(services?.total?.toString())} `
                  : ` ${services?.total ? formatThousand(services.total) : 0} `}
                {symbolAzn}
              </p>
            </div>
            {services?.total === 0 ? (
              <></>
            ) : (
              <div className='chart-wrapper'>
                <Chart
                  options={{
                    labels: services?.list?.map(
                      (item) =>
                        `${item.name} : ${item.count ? formatThousand(item.count?.toString()) : 0} -bilet`,
                    ) || ["No Data"],
                    legend: {
                      show: true,
                      position: "right",
                      horizontalAlign: "left",
                      fontSize: "13px",
                      width: 400,
                    },
                    dataLabels: {
                      enabled: true,
                      style: {
                        fontSize: "13px",
                        colors: ["#000"],
                      },
                      dropShadow: {
                        enabled: false,
                      },
                    },
                    tooltip: {
                      y: {
                        formatter: () => ``,
                      },
                    },
                    plotOptions: {
                      pie: {
                        customScale: 0.9,
                        donut: {
                          size: "35%",
                        },
                      },
                    },
                    chart: {
                      toolbar: {
                        show: true,
                        tools: {
                          download: true,
                        },
                      },
                    },
                  }}
                  series={services?.list?.map((item) => item.count ?? 0) || [0]}
                  type='donut'
                  width='100%'
                />
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FinanceStatisticTicket;
