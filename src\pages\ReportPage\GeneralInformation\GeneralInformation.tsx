import { REPORT } from "@/constants/report";
import useReportGrid from "@/hooks/queryhooks/useReportGrid";
import { useSearchParamsStore } from "@/store/searchParamsStore";
import { formatNumber, formatThousand } from "@/utils/formatNumber";
import { Table } from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs, { Dayjs } from "dayjs";
import React from "react";

import "./GeneralInformation.scss";
import NoDataMessage from "./no-data";
import { usePaginationStore } from "@/store/paginationStore";
import { Loading } from "@/components/CustomLoading";
import GridOverFlow from "@/components/GridOverflow/GridOverFlow";
import { addSpaceBeforeParentheses } from "@/utils/addSpaceBeforeParentheses";

interface DataType {
  key?: string;
  code: string;
  regDate: string;
  userName: string;
  userSurname: string;
  categoryName: string;
  formatName: string;
  entryExitTimestamp: string;
  doorNumber: number;
  visitId: number;
  eventType: number;
  serviceCount: number;
  totalServicePrice: number;
}

const columns: ColumnsType<DataType> = [
  {
    title: "Biletin nömrəsi",
    dataIndex: "code",
    key: "code",
    width: 150,
    align: "left",
    render: (text: string) => <GridOverFlow text={text} />,
  },
  {
    title: "Biletin çap edildiyi zaman",
    dataIndex: "regDate",
    key: "regDate",
    width: 200,
    align: "right",
    render: (text: string) => <GridOverFlow text={text} />,
  },
  {
    title: "İstifadəçi adı",
    dataIndex: "userName",
    key: "userName",
    width: 120,
    align: "center",
    render: (text: string) => <GridOverFlow text={text} />,
  },
  {
    title: "Ziyarətçi kateqoriyası",
    dataIndex: "categoryName",
    key: "categoryName",
    width: 180,
    align: "left",
    render: (text: string) => (
      <div style={{ width: "180px" }}>
        <GridOverFlow text={addSpaceBeforeParentheses(text)} />
      </div>
    ),
  },
  {
    title: "Ziyarət forması",
    dataIndex: "formatName",
    key: "formatName",
    width: 100,
    align: "center",
    render: (text: string) => <GridOverFlow text={addSpaceBeforeParentheses(text)} />,
  },
  {
    title: "Qiymət",
    dataIndex: "price",
    key: "price",
    width: 120,
    align: "right",
    render: (text: string) => <GridOverFlow text={`${formatNumber(text)} ₼`} />,
  },
  {
    title: "Status",
    dataIndex: "eventType",
    key: "eventType",
    width: 100,
    align: "right",
  },
  {
    title: "Ziyarət Tarixi",
    dataIndex: "entryExitTimestamp",
    key: "entryExitTimestamp",
    width: 160,
    align: "right",
    render: (text: string) => <GridOverFlow text={text} />,
  },
  {
    title: "Keçid Məntəqəsi",
    dataIndex: "doorNumber",
    key: "doorNumber",
    width: 90,
    align: "right",
    render: (text: string) => <GridOverFlow text={text} />,
  },
  {
    title: "Bilet dəstinin nömrəsi",
    dataIndex: "visitId",
    key: "visitId",
    width: 120,
    align: "right",
    render: (text: string) => <GridOverFlow text={formatThousand(text)} />,
  },
  {
    title: "Əlavə xidmətlərin sayı",
    dataIndex: "serviceCount",
    key: "serviceCount",
    width: 120,
    align: "right",
    render: (text: string) => <GridOverFlow text={formatThousand(text)} />,
  },
  {
    title: "Əlavə xidmətl üzrə cəmi məbləğ",
    dataIndex: "totalServicePrice",
    key: "totalServicePrice",
    width: 120,
    align: "right",
    render: (text: string) => <GridOverFlow text={`${formatNumber(text)} ₼`} />,
  },
];
const GeneralInformation: React.FC = () => {
  const { pageSize, setPageSize } = usePaginationStore();

  const { startDate, endDate, reportType } = useSearchParamsStore();

  const { data, isFetching } = useReportGrid({
    startDate,
    endDate,
    page: pageSize.page,
    size: pageSize.size,
    enabled: reportType === REPORT.general,
  });

  const formattedData =
    data?.content.map((item, index) => {
      const formatDate = (dateValue: string | Dayjs) => {
        // Return empty string for null/undefined
        if (dateValue === null || dateValue === undefined) {
          return "";
        }

        // Handle empty string
        if (typeof dateValue === "string" && dateValue.trim() === "") {
          return "";
        }

        // Try to parse the date
        const date = dayjs(dateValue);

        // Return formatted date if valid, otherwise empty string
        return date.isValid() ? date.format("DD.MM.YYYY HH:mm:ss") : "";
      };

      return {
        ...item,
        key: String(index),
        regDate: formatDate(item.regDate),
        entryExitTimestamp: formatDate(item.entryExitTimestamp),
      };
    }) || [];

  const paginationConfig = {
    current: pageSize.page,
    pageSize: pageSize.size,
    total: data?.totalElements ?? 0, // Ensure this is set to the total number of items
    showSizeChanger: true,
    pageSizeOptions: ["10", "20", "50", "100"],
    showTotal: (total: number, range: [number, number]) =>
      `${range[0]}-${range[1]} sətirlər  (cəmi: ${total} sətir)`,
    onChange: (page: number, size: number) => {
      setPageSize({
        page,
        size,
      });
    },
  };

  return (
    <div className='table-wrapper'>
      {isFetching ? (
        <Loading />
      ) : (
        <>
          {formattedData.length > 0 ? (
            <Table
              className='custom-table'
              columns={columns}
              dataSource={formattedData}
              pagination={paginationConfig}
              scroll={{
                y: 310,
                x: "max-content",
              }}
            />
          ) : (
            <NoDataMessage />
          )}
        </>
      )}
    </div>
  );
};

export default GeneralInformation;
