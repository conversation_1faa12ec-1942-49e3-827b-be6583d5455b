import { selectTypes } from "@/constants/selectTypes";
import { ReportType, useSearchParamsStore } from "@/store/searchParamsStore";
import "./RadioButtonGroup.scss";
import { Tooltip } from "antd";

const RadioButtonReport = ({ isDisabled }: { isDisabled: boolean }) => {
  const { reportType, setReportType } = useSearchParamsStore();
  return (
    <div className='radio-button-group'>
      {selectTypes.map((option) => (
        <Tooltip title={isDisabled ? "Tarix seçin" : "Hesabat"} key={option.value}>
          <label className={`radio-button ${reportType === option.value ? "active" : ""}`}>
            <input
              disabled={isDisabled}
              type='radio'
              name='radioGroup'
              value={option.value}
              checked={reportType === option.label}
              onChange={() => {
                setReportType(option.value as ReportType);
              }}
              className='hidden'
            />

            {option.label}
          </label>
        </Tooltip>
      ))}
    </div>
  );
};

export default RadioButtonReport;
