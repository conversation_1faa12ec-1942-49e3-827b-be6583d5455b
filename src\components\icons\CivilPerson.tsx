import { colors } from "@/constants/colors";
import { IconProps } from "@/types/iconTypes/icon-types";
import { FC } from "react";

const CivilPerson: FC<IconProps> = ({ active }) => {
  const colorIcon = active ? colors.main_green : colors.white;
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='33' height='36' viewBox='0 0 33 36' fill='none'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M24.2071 17.0227C23.9378 14.85 22.8121 12.9902 21.2075 11.7429C19.6051 10.4934 17.5281 9.8557 15.3561 10.1264C13.1814 10.3957 11.3244 11.5214 10.0742 13.1238C8.8262 14.7262 8.18847 16.8054 8.45776 18.9759V18.978C8.72917 21.1507 9.85487 23.0105 11.4573 24.2578C13.0597 25.5073 15.1368 26.145 17.3094 25.8743C19.4842 25.605 21.3432 24.4793 22.5913 22.8769C23.8386 21.2716 24.4764 19.1946 24.2071 17.0227ZM17.6139 16.3245C17.7235 17.1457 17.0556 17.2413 16.8306 17.4045C15.4496 18.3952 18.6285 20.103 19.2381 19.7845C19.7162 19.5356 20.0889 17.3053 20.6331 19.4737C20.8785 20.4525 21.2188 21.3673 21.6153 22.2462C20.5529 23.5673 18.9962 24.4927 17.1828 24.7219C17.173 24.724 17.1632 24.7261 17.1512 24.7261C16.029 24.8639 14.9392 24.7184 13.9485 24.3457C14.0982 24.0546 14.2874 23.8015 14.5482 23.6201C16.4178 22.0676 17.2391 20.0187 13.8985 20.1382C12.2406 20.238 11.6303 18.3045 11.0361 19.3071C10.7211 19.8415 10.4638 20.4012 10.243 20.9791C9.92378 20.3252 9.70511 19.6116 9.60948 18.8508C9.60948 18.8409 9.60737 18.8311 9.60526 18.8205C9.56308 18.4655 9.54761 18.1132 9.55956 17.768C9.9723 17.7855 10.3885 17.7441 10.8132 17.6224C11.9432 17.2997 11.3715 15.705 13.6335 14.3754C15.2021 13.4529 15.9517 12.5416 16.761 11.2359C18.1503 11.3217 19.4455 11.8322 20.4981 12.6513C20.9523 13.0064 21.3608 13.4184 21.7138 13.879L20.0298 14.445C18.9892 14.8437 17.476 15.3105 17.6139 16.3245ZM16.3321 4.25672C15.1663 4.25672 14.2213 3.31172 14.2213 2.14594C14.2213 0.980156 15.1663 0.0351562 16.3321 0.0351562C17.4978 0.0351562 18.4428 0.980156 18.4428 2.14594C18.4428 3.31172 17.4985 4.25672 16.3321 4.25672ZM11.1022 7.73227C10.783 7.28578 10.8807 6.65016 11.3335 6.34078L13.9604 4.54289C14.2375 4.35375 14.5208 4.25602 14.8555 4.25602H16.3321H17.8107C18.1454 4.25602 18.4288 4.35375 18.7037 4.54289L21.3306 6.34078C21.7827 6.64945 21.8825 7.28578 21.564 7.73227C21.2455 8.17875 20.631 8.26242 20.1711 7.96359L19.1846 7.32375L18.3493 6.76195C18.0842 6.58266 17.7257 6.65227 17.5464 6.91734C17.3692 7.18242 17.4388 7.54313 17.7039 7.72031L18.9653 8.57109V9.45422C18.1321 9.07523 17.2314 8.8882 16.3321 8.8882C15.4328 8.8882 14.5342 9.07523 13.6989 9.45422V8.57109L14.9624 7.72031C15.2275 7.54313 15.2971 7.18242 15.1178 6.91734C14.9385 6.65227 14.5799 6.58266 14.3148 6.76195L13.4795 7.32375L12.493 7.96359C12.0332 8.26242 11.4214 8.17875 11.1022 7.73227ZM28.2353 11.1291C27.651 10.1187 27.9983 8.82703 29.0066 8.24484C30.017 7.66266 31.3086 8.00789 31.8908 9.01828C32.4751 10.0266 32.1278 11.3182 31.1174 11.9025C30.1084 12.484 28.8175 12.1373 28.2353 11.1291ZM25.3229 11.4075C25.4621 11.1206 25.3426 10.774 25.0557 10.632L24.1529 10.1918L23.1046 9.65742C22.6159 9.40641 22.381 8.83406 22.6082 8.33625C22.8353 7.83633 23.435 7.60289 23.9293 7.83984L26.7995 9.21727C27.1004 9.36281 27.3275 9.55828 27.4949 9.84937L28.9714 12.4087C29.1388 12.6977 29.1964 12.993 29.1704 13.3256L28.9271 16.5009C28.8857 17.0473 28.385 17.4495 27.8387 17.396C27.2924 17.344 26.9141 16.856 26.9415 16.3076L27.0034 15.132L27.0709 14.1293C27.0948 13.8101 26.8536 13.533 26.5351 13.5113C26.2159 13.4895 25.9389 13.7285 25.9171 14.0477L25.8116 15.5665L25.048 16.0087C24.958 15.0982 24.6711 14.2228 24.2211 13.4459C23.7725 12.6668 23.1587 11.9812 22.4155 11.449L23.1791 11.0067L24.5481 11.6761C24.8371 11.8146 25.1837 11.6951 25.3229 11.4075ZM31.8908 26.9817C31.3086 27.9921 30.017 28.3373 29.0066 27.7552C27.9983 27.1709 27.651 25.8813 28.2353 24.8709C28.8175 23.8605 30.1091 23.5153 31.1174 24.0975C32.1278 24.6818 32.4744 25.9734 31.8908 26.9817ZM28.9728 23.592L27.4963 26.1513C27.3289 26.4403 27.1018 26.6379 26.8009 26.7834L23.9307 28.1588C23.4364 28.3957 22.8367 28.1644 22.6096 27.6645C22.3825 27.1638 22.6173 26.5922 23.106 26.3433L24.1543 25.8089L25.0571 25.3666C25.344 25.2274 25.4635 24.8801 25.3243 24.5932C25.1851 24.3063 24.8378 24.1868 24.5509 24.326L23.1819 24.9954L22.4183 24.5531C23.1615 24.0209 23.7753 23.3353 24.2239 22.5562C24.6746 21.7772 24.9615 20.9039 25.0508 19.9934L25.8144 20.4335L25.9199 21.9544C25.9417 22.2736 26.2187 22.5127 26.5379 22.4909C26.8571 22.4691 27.0983 22.192 27.0737 21.8728L27.0062 20.8702L26.9443 19.6945C26.9162 19.1461 27.2952 18.6581 27.8415 18.604C28.3878 18.552 28.8878 18.9548 28.93 19.5012L29.1732 22.6765C29.1978 23.0077 29.1402 23.3009 28.9728 23.592ZM16.3321 31.7433C17.4978 31.7433 18.4428 32.6883 18.4428 33.8541C18.4428 35.0198 17.4978 35.9648 16.3321 35.9648C15.1663 35.9648 14.2213 35.0198 14.2213 33.8541C14.2213 32.6883 15.1663 31.7433 16.3321 31.7433ZM21.564 28.2677C21.8832 28.7142 21.7834 29.3498 21.3306 29.6592L18.7037 31.4571C18.4288 31.6463 18.1454 31.744 17.8107 31.744H16.3321H14.8555C14.5208 31.744 14.2375 31.6463 13.9604 31.4571L11.3335 29.6592C10.8814 29.3505 10.7837 28.7142 11.1022 28.2677C11.4214 27.8212 12.0332 27.7376 12.4937 28.0364L13.4802 28.6763L14.3155 29.238C14.5806 29.4173 14.9392 29.3477 15.1185 29.0827C15.2978 28.8176 15.2282 28.4569 14.9631 28.2776L13.6996 27.4282V26.5451C14.5349 26.922 15.4335 27.1111 16.3328 27.1111C17.2321 27.1111 18.1328 26.922 18.966 26.5451V27.4282L17.7046 28.2776C17.4395 28.4569 17.3699 28.8176 17.5471 29.0827C17.7264 29.3477 18.085 29.4173 18.35 29.238L19.1853 28.6763L20.1718 28.0364C20.6317 27.7376 21.2455 27.8212 21.564 28.2677ZM4.43167 24.8709C5.01386 25.8813 4.66862 27.1709 3.65823 27.7552C2.64995 28.3373 1.35831 27.9921 0.774015 26.9817C0.191827 25.9734 0.537061 24.6818 1.54745 24.0975C2.55784 23.516 3.84737 23.8605 4.43167 24.8709ZM7.3412 24.5925C7.20198 24.8794 7.32151 25.226 7.60839 25.3659L8.51331 25.8082L9.56167 26.3426C10.0503 26.5915 10.2831 27.1638 10.056 27.6637C9.82886 28.1637 9.22909 28.395 8.7348 28.158L5.86464 26.7827C5.5637 26.6372 5.33659 26.4396 5.17136 26.1506L3.69269 23.5912C3.52534 23.3002 3.46769 23.007 3.4937 22.6744L3.73698 19.4991C3.77847 18.9527 4.2812 18.5505 4.82683 18.6019C5.37105 18.656 5.75144 19.144 5.7219 19.6924L5.66214 20.868L5.59253 21.8707C5.57073 22.1899 5.8098 22.467 6.12831 22.4888C6.44753 22.5105 6.72456 22.2715 6.74636 21.9523L6.85183 20.4314L7.61683 19.9913C7.70683 20.9018 7.99159 21.7751 8.4423 22.5541C8.893 23.3332 9.50472 24.0188 10.25 24.551L9.48503 24.9933L8.11745 24.3239C7.82987 24.1854 7.48323 24.3049 7.3412 24.5925ZM0.774015 9.01828C1.35831 8.00789 2.64925 7.66125 3.65823 8.24484C4.66862 8.82703 5.01386 10.1187 4.43167 11.1291C3.84737 12.1373 2.55784 12.4847 1.54745 11.9025C0.537061 11.3182 0.191827 10.0266 0.774015 9.01828ZM3.69198 12.408L5.17066 9.84867C5.33589 9.55758 5.563 9.36211 5.86394 9.21656L8.73409 7.83914C9.22839 7.60219 9.82816 7.83492 10.0553 8.33555C10.2824 8.83406 10.0496 9.4057 9.56097 9.65672L8.51261 10.1911L7.60769 10.6312C7.32081 10.7726 7.20128 11.1192 7.3405 11.4068C7.48183 11.6937 7.82917 11.8132 8.11605 11.674L9.48362 11.0046L10.2486 11.4469C9.50331 11.9791 8.89159 12.6647 8.44089 13.4437C7.99019 14.2214 7.70542 15.0961 7.61542 16.0066L6.85042 15.5644L6.74495 14.0456C6.72316 13.7264 6.44612 13.4873 6.1269 13.5091C5.80769 13.5309 5.56862 13.808 5.59112 14.1272L5.66073 15.1298L5.7205 16.3055C5.75073 16.8532 5.36964 17.3419 4.82542 17.3939C4.27909 17.448 3.77706 17.0452 3.73558 16.4988L3.4923 13.3235C3.46698 12.9923 3.52464 12.697 3.69198 12.408Z'
        fill={colorIcon}
      />
    </svg>
  );
};

export default CivilPerson;
