import { QueryKeys } from "@/enums/queryKeyEnums";
import { ReportServiceController } from "@/services/report-service/report-service";
import { SearchParmasType } from "@/types";
import { useQuery } from "@tanstack/react-query";

const useReportGrid = ({
  startDate,
  endDate,
  page,
  size,
  enabled,
}: {
  startDate: SearchParmasType;
  endDate: SearchParmasType;
  page?: number;
  size?: number;
  enabled: boolean;
}) =>
  useQuery({
    queryKey: [QueryKeys.REPORT_GRID, page, size],
    queryFn: () => ReportServiceController.getReportGrid(startDate, endDate, page, size),
    enabled,
  });

export default useReportGrid;
