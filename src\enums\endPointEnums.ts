export enum Endpoints {
  VISIT_FORMAT = "/visit-format",
  FILE = "/file",
  VISIT_CATEGORY = "/visit-category",
  SERVICE = "/service",
  DEVICE = "/device",
  VISIT = "/visit",
  AUTH = "/public/login",
  NOTIFICATION = "/notification",
  NOTIFICATION_READ_ALL = "/notification/read/all",
  NOTIFICATION_READ = "/notification/read",
  NOTIFICATION_COUNT = "/notification/count",
  REPORT_SERVICES = "/visit/report/services",
  REPORT_FORMATS = "/visit/report/formats",
  REPORT_CATEGORIES = "/visit/report/categories",
  REPORT_SERVICES_STATISTIC = "/entry-exit-log/report/services",
  REPORT_FORMATS_STATISTIC = "/entry-exit-log/report/formats",
  REPORT_CATEGORIES_STATISTIC = "/entry-exit-log/report/categories",
  REPORT_EXCEL_FINANCE = "/visit/report/excel",
  REPORT_EXCEL_STATISTICS = "/entry-exit-log/report/visit/excel",
  REPORT_GENERAL = "/entry-exit-log/report/details/excel",
  REPORT_BY_DATE_EXCEL = "/visit/report/daily-tickets/excel",
  AUTH_LOGOUT = "/user/log-out",
  REPORT_SERVICES_TICKET_STATISTIC = "/visit/report/service/tickets",
  REPORT_FORMATS_TICKET_STATISTIC = "/visit/report/format/tickets",
  REPORT_CATEGORIES_TICKET_STATISTIC = "/visit/report/category/tickets",
  REPORT_EXCEL_TICKET = "/visit/report/ticket/excel",
  REPORT_COUNT_MONEY_SERVICES = "/visit/report/services/count-money",
  REPORT_COUNT_MONEY_CATEGORIES = "/visit/report/categories/count-money",
  REPORT_COUNT_MONEY_FORMATS = "/visit/report/formats/count-money",
  REPORT_EXCEL_COUNT = "/visit/report/count-money/excel",
  REPORT_GRID = "/entry-exit-log",
  REPORT_BY_DATE = "/visit/report/daily-ticket",
  REPORT_RESERVED_TICKET = "/visit/report/reserved-tickets",
}
