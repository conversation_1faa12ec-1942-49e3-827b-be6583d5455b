import { Endpoints } from "@/enums/endPointEnums";
import { SearchParmasType, TGetReportByDateController } from "@/types";
import { AxiosResponse } from "axios";
import { httpClient } from "../httpclient";

export class ReportByDateController {
  static async getReportGrid(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
    page?: number,
    size?: number,
  ): Promise<TGetReportByDateController> {
    const response: AxiosResponse<TGetReportByDateController> = await httpClient.get(
      `${Endpoints.REPORT_BY_DATE}?startDate=${startDate}&endDate=${endDate}&page=${page}&size=${size}`,
    );
    return response.data;
  }
}
