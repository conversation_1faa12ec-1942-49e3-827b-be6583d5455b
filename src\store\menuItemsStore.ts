import { TContent } from "@/types";
import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

type MenuItemsSlice = {
  menus: TContent[];
  setMenus: (key: TContent[]) => void;
};

const createMenuItemsSlice: StateCreator<MenuItemsSlice, [["zustand/devtools", never]]> = (
  set,
) => ({
  menus: [],

  setMenus: (key) => set({ menus: key }, false, `menuSetState ${key}`),
});

export const useMenusStore = create<MenuItemsSlice>()(devtools(createMenuItemsSlice));
