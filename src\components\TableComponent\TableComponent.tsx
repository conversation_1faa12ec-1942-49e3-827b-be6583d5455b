import React, { ReactElement } from "react";
import { Table } from "antd";
import CustomNumberInput from "../CustomNumberInput/CustomNumberInput";
import "./TableComponent.scss";
import { ColumnsType } from "antd/es/table";
import { TContentVisitCategory, TVisitCategoryConditions } from "@/types";
import CustomInformation from "../CustomInformation/CustomInformation";
import { addSpaceBeforeParentheses } from "@/utils/addSpaceBeforeParentheses";

interface DataType {
  key: string;
  name: string | ReactElement;
  address: string | ReactElement;
  visitCategoryPriceConditions: TVisitCategoryConditions[];
  pricePerPerson: number | string;
  condition?: { id: string; name: string };
}

const columns: ColumnsType<DataType> = [
  {
    title: "Ziyarətçi kateqoriyası",
    dataIndex: "name",
    key: "category",
    width: "80%",
    align: "left",
    render: (text: ReactElement) => {
      return addSpaceBeforeParentheses(text.props.text);
    },
  },
  {
    title: "Qiymət",
    dataIndex: "pricePerPerson",
    key: "pricePerPerson",
    align: "center",
    width: "10%",
  },
  {
    title: "Ziyarətçi sayı",
    dataIndex: "address",
    key: "address",
    align: "right",
    width: "10%",
  },
];

const MyTable: React.FC<{ datas?: TContentVisitCategory[] }> = ({ datas }) => {
  const data = datas?.map((item) => {
    const priceConditions: string[] = [];
    let message = "Say ";
    if (item.visitCategoryPriceConditions) {
      message += `${item.visitCategoryPriceConditions[0]?.to} nəfər daxil olmaqla ümumi olaraq ${item.visitCategoryPriceConditions[0]?.price}₼, daha çox olduqda ${item.visitCategoryPriceConditions[1]?.price}₼ ilə nəzərə alınacaqdır `;
      item.visitCategoryPriceConditions.forEach((item) => {
        priceConditions.push(`${item.price.toFixed(2)} ₼ `);
      });
    }
    return {
      key: item.id.toString(),
      name: (
        <CustomInformation
          info={!!item.visitCategoryPriceConditions.length}
          text={item.name}
          tooltip={message}
        />
      ),
      address: <CustomNumberInput {...item} />,
      visitCategoryPriceConditions: item.visitCategoryPriceConditions,
      pricePerPerson:
        item.pricePerPerson !== null
          ? item.pricePerPerson.toFixed(2) + " ₼"
          : priceConditions.join("/ "),
    };
  });
  return (
    <div className='table-wrapper'>
      <Table<DataType> columns={columns} dataSource={data} pagination={false} />
    </div>
  );
};

export default MyTable;
