import { Endpoints } from "@/enums/endPointEnums";
import { SearchParmasType, TGetReportController } from "@/types";
import { AxiosResponse } from "axios";
import { httpClient } from "../httpclient";

export class ReportServiceController {
  static async getReportGrid(
    startDate: SearchParmasType,
    endDate: SearchParmasType,
    page?: number,
    size?: number,
  ): Promise<TGetReportController> {
    const response: AxiosResponse<TGetReportController> = await httpClient.get(
      `${Endpoints.REPORT_GRID}?startDate=${startDate}&endDate=${endDate}&page=${page}&size=${size}`,
    );
    return response.data;
  }
}
