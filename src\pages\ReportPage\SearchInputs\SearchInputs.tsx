import { FileExcelOutlined, SearchOutlined } from "@ant-design/icons";
import { useQueryClient } from "@tanstack/react-query";
import { Button, DatePicker } from "antd";
import dayjs, { type Dayjs } from "dayjs";
import { useEffect } from "react";

import { REPORT } from "@/constants/report";
import { QueryKeys } from "@/enums/queryKeyEnums";
import { useReportHooks } from "@/hooks/useReportHooks";
import { FileService } from "@/services/file-controller/fileService";
import { useInformationStore } from "@/store/informationStore";
import { usePaginationStore } from "@/store/paginationStore";
import { useSearchParamsStore } from "@/store/searchParamsStore";
import { utilFileName } from "@/utils/reportFileNameGenerator";
import SelectReport from "../select/SelectReport";
import "./SearchInput.scss";

const { RangePicker } = DatePicker;

const SearchInputs = () => {
  const queryClient = useQueryClient();
  const { reportType } = useSearchParamsStore();
  const { startDate, endDate, setStartDate, setEndDate } = useSearchParamsStore();
  const { setCategories, setFormats, setServices } = useInformationStore();
  const { setPageSize } = usePaginationStore();
  const { reportData, refetchReportData } = useReportHooks({ startDate, endDate, reportType });

  useEffect(() => {
    const { categories, formats, services } = reportData;

    if (categories && formats && services) {
      setCategories(categories);
      setFormats(formats);
      setServices(services);
    }
  }, [reportData, setCategories, setFormats, setServices]);

  const handleDateChange = (dates: [Dayjs | null, Dayjs | null] | null) => {
    if (dates && dates[0] && dates[1]) {
      setStartDate(dates[0].format("YYYY-MM-DD"));
      setEndDate(dates[1].format("YYYY-MM-DD"));
    } else {
      setStartDate(null);
      setEndDate(null);
    }
  };

  const handleSearch = () => {
    if (reportType === REPORT.general) {
      setPageSize({ page: 1, size: 20 });
      queryClient.invalidateQueries({ queryKey: [QueryKeys.REPORT_GRID] });
    } else if (reportType === REPORT.by_date) {
      setPageSize({ page: 1, size: 20 });
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.REPORT_GRID_BY_DATE],
      });
    } else {
      refetchReportData();
    }
  };

  const handleDownloadExcel = () => {
    FileService.downloadReportFile({
      startDate,
      endDate,
      typeReport: reportType,
      fileName: utilFileName(reportType),
    });
  };
  const isDisabledButtons = !startDate && !endDate;

  return (
    <div className='search-input'>
      <SelectReport />
      <RangePicker
        className='inputs'
        defaultValue={[dayjs().subtract(0, "day"), dayjs()]}
        onChange={handleDateChange}
        format='DD.MM.YYYY'
      />
      <Button
        className='input-btn'
        disabled={isDisabledButtons}
        icon={<SearchOutlined />}
        onClick={handleSearch}
      >
        Axtar
      </Button>
      <Button
        className={`excel-btn ${isDisabledButtons ? "disabled-excel" : ""}`}
        disabled={isDisabledButtons}
        icon={<FileExcelOutlined />}
        onClick={handleDownloadExcel}
      >
        Excel
      </Button>
    </div>
  );
};

export default SearchInputs;
