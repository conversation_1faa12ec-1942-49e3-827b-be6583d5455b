import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

type PageSize = {
  page: number;
  size: number;
};

type PaginationSlice = {
  pageSize: PageSize;
  setPageSize: (key: PageSize) => void;
};

const createPaginationSlice: StateCreator<PaginationSlice, [["zustand/devtools", never]]> = (
  set,
) => ({
  pageSize: {
    page: 1,
    size: 20,
  },
  setPageSize: (key) => set({ pageSize: key }, false, `pageSizeState ${JSON.stringify(key)}`),
});

export const usePaginationStore = create<PaginationSlice>()(devtools(createPaginationSlice));
