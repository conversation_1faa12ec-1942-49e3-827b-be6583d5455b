import useReportReservedTicket from "@/hooks/queryhooks/useReportReserved";
import { formatDateWithTime } from "@/utils/formatDateWIthTime";
import { InfoCircleOutlined } from "@ant-design/icons";
import "./ReservedInfoTicket.scss";
import { formatThousand } from "@/utils/formatNumber";
const ReservedInfoTicket = () => {
  const { data: reservedTicketData } = useReportReservedTicket();

  if (!reservedTicketData) {
    return "";
  }

  const [key, value] = Object.entries(reservedTicketData)[0];
  const dateReserved = formatDateWithTime(key);
  const reservedCount = value;
  return (
    <div className='reserved-info-ticket'>
      <span className='reserved-info'>
        <InfoCircleOutlined className='pulse' color='green' style={{ marginRight: "5px" }} />
        Satıla biləcək biletlər
      </span>
      <span className='reserved-count'>
        {dateReserved} olan məlumat: <strong>{formatThousand(reservedCount)}</strong> bilet
      </span>
    </div>
  );
};

export default ReservedInfoTicket;
