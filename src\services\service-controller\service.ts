import { Endpoints } from "@/enums/endPointEnums";
import { TGetService, TrequestBodyPutService, TResponseBodyService } from "@/types";
import { AxiosResponse } from "axios";
import { httpClient } from "../httpclient";

export class ServiceController {
  static async get(): Promise<TGetService> {
    const response: AxiosResponse<TGetService> = await httpClient.get(`${Endpoints.SERVICE}`);
    return response.data;
  }
  static async put(
    id: number | string,
    postBody: TrequestBodyPutService,
  ): Promise<TResponseBodyService> {
    const response: AxiosResponse<TResponseBodyService> = await httpClient.put(
      `${Endpoints.SERVICE}/${id}`,
      postBody,
    );
    return response.data;
  }
  static async patch(id: string | number): Promise<TResponseBodyService> {
    const response: AxiosResponse<TResponseBodyService> = await httpClient.patch(
      `${Endpoints.SERVICE}/${id}`,
    );
    return response.data;
  }
  static async post(postData: TrequestBodyPutService): Promise<TResponseBodyService> {
    const response: AxiosResponse<TResponseBodyService> = await httpClient.post(
      `${Endpoints.SERVICE}`,
      postData,
    );
    return response.data;
  }
}
