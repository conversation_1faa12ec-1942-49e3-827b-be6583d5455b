export function formatNumber(value: number | string) {
  // Convert to a valid number safely
  const num = Number(value) || 0;

  // Format with space as a thousand separator and comma as decimal
  return num.toLocaleString("fr-FR", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

export function formatThousand(value: number | string) {
  // Convert to a valid number safely
  const num = Number(value) || 0;

  // Format with space as a thousand separator and comma as decimal
  return num.toLocaleString("fr-FR");
}
