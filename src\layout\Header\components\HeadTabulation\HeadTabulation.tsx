import { navigationPath, tabItems } from "@/constants/navigationPath";
import { useActiveTabKeyStore } from "@/store/activeTabkey";
import { useSearchParamsStore } from "@/store/searchParamsStore";
import { useVisitorStore } from "@/store/visitorsStore";
import { Tabs } from "antd";
import React from "react";
import { useNavigate } from "react-router-dom";
import "./HeadTabulation.scss";

const HeadTabulation: React.FC = () => {
  const navigate = useNavigate();
  const { activeKey, setActiveKey } = useActiveTabKeyStore();
  const { clearVisitors } = useVisitorStore();
  const { clear } = useSearchParamsStore();

  const onChange = (key: string) => {
    setActiveKey(key);
    clearVisitors();
    clear();
    navigate(navigationPath[key as keyof typeof navigationPath]);
  };
  return <Tabs className='head-tab' activeKey={activeKey} items={tabItems} onChange={onChange} />;
};

export default HeadTabulation;
