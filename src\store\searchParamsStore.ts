import { REPORT } from "@/constants/report";
import { SearchParmasType } from "@/types";
import dayjs from "dayjs";
import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";
export type ReportType = "finance" | "statistics";
type SearchParamsType = {
  reportType: ReportType;
  startDate: SearchParmasType;
  endDate: SearchParmasType;
  setReportType: (key: ReportType) => void;
  setStartDate: (key: SearchParmasType) => void;
  setEndDate: (key: SearchParmasType) => void;
  clear: () => void;
};

const createSearchParamsSlice: StateCreator<SearchParamsType, [["zustand/devtools", never]]> = (
  set,
) => ({
  reportType: REPORT.ticket_finance as ReportType,
  setReportType: (key) => set({ reportType: key }, false, "reportType"),
  startDate: dayjs().subtract(0, "day").format("YYYY-MM-DD"),
  endDate: dayjs().format("YYYY-MM-DD"),
  clear: () =>
    set(
      {
        startDate: dayjs().subtract(0, "day").format("YYYY-MM-DD"),
        endDate: dayjs().format("YYYY-MM-DD"),
      },
      false,
      `EventSearchParamsStateClear ${"clearing"}`,
    ),
  setStartDate: (key) => set({ startDate: key }, false, `StartDate`),
  setEndDate: (key) => set({ endDate: key }, false, `EndDate`),
});

export const useSearchParamsStore = create<SearchParamsType>()(devtools(createSearchParamsSlice));
