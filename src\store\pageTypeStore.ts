import { create, StateCreator } from "zustand";
import { devtools } from "zustand/middleware";

type PageTypeSlice = {
  pageType: string;
  setPageType: (key: string) => void;
};

const createPageTypeSlice: StateCreator<PageTypeSlice, [["zustand/devtools", never]]> = (set) => ({
  pageType: "",
  setPageType: (key) => set({ pageType: key }, false, `pageTypeState ${key}`),
});

export const usePageTypeStore = create<PageTypeSlice>()(devtools(createPageTypeSlice));
