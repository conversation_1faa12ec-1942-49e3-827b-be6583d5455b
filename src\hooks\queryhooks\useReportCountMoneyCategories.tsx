import { QueryKeys } from "@/enums/queryKeyEnums";
import { Visit } from "@/services/visit-controller/vistiController";
import { SearchParmasType } from "@/types";
import { useQuery } from "@tanstack/react-query";

const useReportCountMoneyCategories = ({
  startDate,
  endDate,
  enabled,
}: {
  startDate: SearchParmasType;
  endDate: SearchParmasType;
  enabled: boolean;
}) => {
  return useQuery({
    queryKey: [QueryKeys.REPORT_CATEGORIES_COUNT_MONEY],
    queryFn: () => Visit.getCountMoneyCategories(startDate, endDate),
    enabled,
  });
};
export default useReportCountMoneyCategories;
