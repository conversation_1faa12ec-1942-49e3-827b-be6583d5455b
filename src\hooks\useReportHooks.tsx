import { useState, useEffect } from "react";
import { REPORT } from "@/constants/report";

// Finance report hooks
import useReportCategories from "@/hooks/queryhooks/useReportCategories";
import useReportFormats from "@/hooks/queryhooks/useReportFormats";
import useReportServices from "@/hooks/queryhooks/useReportServices";

// Statistics report hooks
import useReportCategoriesStatistic from "@/hooks/queryhooks/useReportCategoriesStat";
import useReportFormatsStatistic from "@/hooks/queryhooks/useReportFormatsStatistic";
import useReportServicesStatistic from "@/hooks/queryhooks/useReportServicesStatistic";

// Ticket report hooks
import useReportCategoriesTicket from "@/hooks/queryhooks/useReportCategorieTicket";
import useReportFormatsTicket from "@/hooks/queryhooks/useReportFormatTicket";
import useReportServicesTicket from "@/hooks/queryhooks/useReportServiceTicet";

// Ticket-finance report hooks
import useReportCountMoneyCategories from "@/hooks/queryhooks/useReportCountMoneyCategories";
import useReportCountMoneyFormats from "@/hooks/queryhooks/useReportCountMoneyFormat";
import useReportCountMoneyServices from "@/hooks/queryhooks/useReportMoneyServices";
import { Dayjs } from "dayjs";
import { ReportResponseType } from "@/types";

interface UseReportHooksProps {
  startDate: Dayjs | null | string;
  endDate: Dayjs | null | string;
  reportType: string;
}

export const useReportHooks = ({ startDate, endDate, reportType }: UseReportHooksProps) => {
  const [enabled, setEnabled] = useState(true);
  const [reportData, setReportData] = useState<{
    categories: ReportResponseType | null | undefined;
    formats: ReportResponseType | null | undefined;
    services: ReportResponseType | null | undefined;
  }>({
    categories: null,
    formats: null,
    services: null,
  });

  // Finance report queries
  const { data: financeCategories, refetch: refetchFinanceCategories } = useReportCategories({
    startDate,
    endDate,
    enabled: enabled && reportType === REPORT.finance,
  });

  const { data: financeFormats, refetch: refetchFinanceFormats } = useReportFormats({
    startDate,
    endDate,
    enabled: enabled && reportType === REPORT.finance,
  });

  const { data: financeServices, refetch: refetchFinanceServices } = useReportServices({
    startDate,
    endDate,
    enabled: enabled && reportType === REPORT.finance,
  });

  // Statistics report queries
  const { data: statisticsCategories, refetch: refetchStatisticsCategories } =
    useReportCategoriesStatistic({
      startDate,
      endDate,
      enabled: enabled && reportType === REPORT.statistics,
    });

  const { data: statisticsFormats, refetch: refetchStatisticsFormats } = useReportFormatsStatistic({
    startDate,
    endDate,
    enabled: enabled && reportType === REPORT.statistics,
  });

  const { data: statisticsServices, refetch: refetchStatisticsServices } =
    useReportServicesStatistic({
      startDate,
      endDate,
      enabled: enabled && reportType === REPORT.statistics,
    });

  // Ticket report queries
  const { data: ticketCategories, refetch: refetchTicketCategories } = useReportCategoriesTicket({
    startDate,
    endDate,
    enabled: enabled && reportType === REPORT.ticket,
  });

  const { data: ticketFormats, refetch: refetchTicketFormats } = useReportFormatsTicket({
    startDate,
    endDate,
    enabled: enabled && reportType === REPORT.ticket,
  });

  const { data: ticketServices, refetch: refetchTicketServices } = useReportServicesTicket({
    startDate,
    endDate,
    enabled: enabled && reportType === REPORT.ticket,
  });

  // Ticket-finance report queries
  const { data: ticketFinanceCategories, refetch: refetchTicketFinanceCategories } =
    useReportCountMoneyCategories({
      startDate,
      endDate,
      enabled: enabled && reportType === REPORT.ticket_finance,
    });

  const { data: ticketFinanceFormats, refetch: refetchTicketFinanceFormats } =
    useReportCountMoneyFormats({
      startDate,
      endDate,
      enabled: enabled && reportType === REPORT.ticket_finance,
    });

  const { data: ticketFinanceServices, refetch: refetchTicketFinanceServices } =
    useReportCountMoneyServices({
      startDate,
      endDate,
      enabled: enabled && reportType === REPORT.ticket_finance,
    });
  useEffect(() => {
    const reportDataObj = {
      [REPORT.finance]: {
        categories: financeCategories,
        formats: financeFormats,
        services: financeServices,
      },
      [REPORT.statistics]: {
        categories: statisticsCategories,
        formats: statisticsFormats,
        services: statisticsServices,
      },
      [REPORT.ticket]: {
        categories: ticketCategories,
        formats: ticketFormats,
        services: ticketServices,
      },
      [REPORT.ticket_finance]: {
        categories: ticketFinanceCategories,
        formats: ticketFinanceFormats,
        services: ticketFinanceServices,
      },
      [REPORT.general]: {
        categories: null,
        formats: null,
        services: null,
      },
      [REPORT.by_date]: {
        categories: null,
        formats: null,
        services: null,
      },
    };
    setReportData(reportDataObj[reportType]);
  }, [
    reportType,
    financeCategories,
    financeFormats,
    financeServices,
    statisticsCategories,
    statisticsFormats,
    statisticsServices,
    ticketCategories,
    ticketFormats,
    ticketServices,
    ticketFinanceCategories,
    ticketFinanceFormats,
    ticketFinanceServices,
  ]);

  const refetchFinance = () => {
    refetchFinanceCategories();
    refetchFinanceFormats();
    refetchFinanceServices();
  };
  const refetchStatistic = () => {
    refetchStatisticsCategories();
    refetchStatisticsFormats();
    refetchStatisticsServices();
  };
  const refetchTicket = () => {
    refetchTicketCategories();
    refetchTicketFormats();
    refetchTicketServices();
  };
  const refetchTicketFinance = () => {
    refetchTicketFinanceCategories();
    refetchTicketFinanceFormats();
    refetchTicketFinanceServices();
  };
  const refetchObj = {
    [REPORT.finance]: refetchFinance,
    [REPORT.statistics]: refetchStatistic,
    [REPORT.ticket]: refetchTicket,
    [REPORT.ticket_finance]: refetchTicketFinance,
  };

  const refetchReportData = () => {
    setEnabled(true);
    refetchObj[reportType]();
  };

  return {
    reportData,
    refetchReportData,
  };
};
