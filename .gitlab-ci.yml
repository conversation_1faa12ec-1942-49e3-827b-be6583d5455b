stages:
  - sonarqube-check
  - build to test
  - build to prod

sonarqube-check:
  stage: sonarqube-check
  image:
    name: sonarsource/sonar-scanner-cli:5.0
    entrypoint: [""]
  variables:
    PROJECT_KEY: ag-smart-front-test
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  script:
    - apk update && apk add jq
    - sonar-scanner -Dsonar.projectKey=$PROJECT_KEY -Dsonar.host.url=$SONAR_HETZNER_URL -Dsonar.login=$SONAR_HETZNER_TOKEN
    - SONARQUBE_STATUS=$(curl -u "${SONAR_HETZNER_TOKEN}:" "$SONAR_HETZNER_URL/api/qualitygates/project_status?projectKey=$PROJECT_KEY" | jq -r '.projectStatus.status')
    - echo $SONARQUBE_STATUS
    - if [ "$SONARQUBE_STATUS" = "ERROR" ]; then
      echo "Kodunuz sonarqube testlərindən keçmədi. Ətraflı məlumat üçün https://sonarqube.aist.group ünvanına daxil olun";
      else
      echo "Kodunuz sonarqube testlərindən keçdi. Ətraflı məlumat üçün https://sonarqube.aist.group ünvanına daxil olun";
      fi


  tags:
    - Hetzner
  only:
    - development
    - master

buildd:
  stage: build to prod
  image: node:20
  script:
    - echo $ag_prod_file > /index_file
    - apt update && apt install  sshpass && apt install openssh-client
    - npm install
    - npm run build:prod
    - cd build
    - sshpass -f '/index_file' ssh -o 'StrictHostKeyChecking=no' -p 2200 $user@$ag_meis_server 'rm -rf /var/www/smart-front/*'
    - sshpass -f '/index_file' scp -P 2200 -r * $user@$ag_meis_server:/var/www/smart-front/
    - sshpass -f '/index_file' ssh -p 2200  $user@$ag_meis_server "sudo systemctl reload nginx"

  tags:
    - Hetzner
  only:
    - master

build:
  stage: build to test
  image: node:20
  script:
    - echo $ag_test_file > /index_file
    - apt update && apt install  sshpass && apt install openssh-client
    - npm install
    - npm run build:dev
    - cd build
    - sshpass -f '/index_file' ssh -o 'StrictHostKeyChecking=no' -p 2200 $user@$aist_test_srv 'rm -rf /var/www/smart-front/*'
    - sshpass -f '/index_file' scp -P 2200 -r * $user@$aist_test_srv:/var/www/smart-front/
    - sshpass -f '/index_file' ssh -p 2200  $user@$aist_test_srv "sudo systemctl reload nginx"

  tags:
    - Hetzner
  only:
    - development

