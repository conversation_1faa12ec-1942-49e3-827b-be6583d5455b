import { colors } from "@/constants/colors";
import { IconProps } from "@/types/iconTypes/icon-types";
import { FC } from "react";

const UndiscountedUser: FC<IconProps> = ({ active }) => {
  const colorIcon = active ? colors.main_green : colors.white;
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='29' height='28' viewBox='0 0 29 28' fill='none'>
      <path
        d='M14.6686 14.5397C18.6686 14.5397 21.9384 11.2699 21.9384 7.26985C21.9384 3.26983 18.6686 0 14.6686 0C10.6686 0 7.3988 3.26983 7.3988 7.26985C7.3988 11.2699 10.6686 14.5397 14.6686 14.5397Z'
        fill={colorIcon}
      />
      <path
        d='M27.1448 20.3492C26.9543 19.873 26.7004 19.4286 26.4147 19.0159C24.9544 16.8571 22.7004 15.4286 20.1608 15.0793C19.8433 15.0476 19.4941 15.1111 19.2401 15.3016C17.9068 16.2857 16.3195 16.7936 14.6687 16.7936C13.0178 16.7936 11.4306 16.2857 10.0972 15.3016C9.84323 15.1111 9.49402 15.0158 9.17659 15.0793C6.6369 15.4286 4.35121 16.8571 2.92265 19.0159C2.63694 19.4286 2.38294 19.9048 2.19251 20.3492C2.09729 20.5397 2.12901 20.7619 2.22423 20.9524C2.47822 21.3968 2.79565 21.8413 3.08136 22.2223C3.52579 22.8255 4.002 23.3651 4.5417 23.873C4.98613 24.3175 5.49405 24.7302 6.00204 25.1429C8.50995 27.0159 11.5258 28 14.637 28C17.7481 28 20.764 27.0159 23.2719 25.1429C23.7798 24.762 24.2877 24.3175 24.7322 23.873C25.2401 23.3651 25.7481 22.8254 26.1925 22.2223C26.51 21.8096 26.7957 21.3968 27.0497 20.9524C27.2083 20.7619 27.2401 20.5397 27.1448 20.3492Z'
        fill={colorIcon}
      />
    </svg>
  );
};

export default UndiscountedUser;
