import { colors } from "@/constants/colors";
import { IconProps } from "@/types/iconTypes/icon-types";
import { FC } from "react";

const GovernmentPerson: FC<IconProps> = ({ active }) => {
  const colorIcon = active ? colors.main_green : colors.white;
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='37' height='36' viewBox='0 0 37 36' fill='none'>
      <path
        d='M31.5521 11.124L18.6461 3.09C18.4541 2.97 18.2081 2.97 18.0161 3.09L5.11013 11.124C4.93613 11.232 4.82812 11.424 4.82812 11.634V13.962C4.82812 14.292 5.09813 14.562 5.42813 14.562H31.2341C31.5641 14.562 31.8341 14.292 31.8341 13.962V11.634C31.8341 11.424 31.7261 11.232 31.5521 11.124ZM18.3341 12.342C16.9601 12.342 15.8441 11.226 15.8441 9.852C15.8441 8.478 16.9601 7.362 18.3341 7.362C19.7081 7.362 20.8241 8.478 20.8241 9.852C20.8241 11.226 19.7081 12.342 18.3341 12.342Z'
        fill={colorIcon}
      />
      <path d='M24.6523 15.762H29.0923V24.912H24.6523V15.762Z' fill={colorIcon} />
      <path d='M16.1143 15.762H20.5543V24.912H16.1143V15.762Z' fill={colorIcon} />
      <path d='M7.57031 15.762H12.0103V24.912H7.57031V15.762Z' fill={colorIcon} />
      <path
        d='M33.334 29.544V32.4C33.334 32.73 33.064 33 32.734 33H3.93398C3.59798 33 3.33398 32.73 3.33398 32.4V29.544C3.33398 29.214 3.59798 28.944 3.93398 28.944H5.37398V26.712C5.37398 26.382 5.64398 26.112 5.97398 26.112H30.694C31.024 26.112 31.294 26.382 31.294 26.712V28.944H32.734C33.064 28.944 33.334 29.214 33.334 29.544Z'
        fill={colorIcon}
      />
    </svg>
  );
};

export default GovernmentPerson;
