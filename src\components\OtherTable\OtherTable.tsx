import { Table } from "antd";
import React, { ReactElement } from "react";
import CustomSwitch from "../CustomSwitch/CustomSwitch";
import "./OtherTabel.scss";
import { ColumnsType } from "antd/es/table";
import { TServiceContent } from "@/types";
interface DataType {
  key: string;
  name: string;
  price: string;
  address: string | ReactElement;
}

const columns: ColumnsType<DataType> = [
  {
    title: "Əlavə xidmətlər",
    dataIndex: "name",
    key: "category",
    width: "80%",
  },
  {
    title: "",
    dataIndex: "price",
    key: "price",
    align: "center",
    width: "10%",
  },
  {
    title: "",
    dataIndex: "address",
    key: "address",
    align: "right",
    width: "10%",
  },
];
const OtherTable: React.FC<{ datas?: TServiceContent[] }> = ({ datas }) => {
  const data = datas?.map((item) => {
    return {
      key: item.id.toString(),
      name: item.name,
      address: <CustomSwitch {...item} />,
      price: item.price.toFixed(2) + " ₼",
    };
  });
  return (
    <div className='table-wrapper'>
      <Table<DataType>
        columns={columns}
        dataSource={data}
        pagination={false}
        // pagination={{ pageSize: 5 }}
      />
    </div>
  );
};

export default OtherTable;
