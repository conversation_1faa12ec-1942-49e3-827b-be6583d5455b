!function(t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).SockJS=t()}(function(){return(function t(e,n,r){function i(o,a){if(!n[o]){if(!e[o]){var u="function"==typeof require&&require;if(!a&&u)return u(o,!0);if(s)return s(o,!0);var c=Error("Cannot find module '"+o+"'");throw c.code="MODULE_NOT_FOUND",c}var l=n[o]={exports:{}};e[o][0].call(l.exports,function(t){return i(e[o][1][t]||t)},l,l.exports,t,e,n,r)}return n[o].exports}for(var s="function"==typeof require&&require,o=0;o<r.length;o++)i(r[o]);return i})({1:[function(t,e,n){(function(n){(function(){"use strict";var r=t("./transport-list");e.exports=t("./main")(r),"_sockjs_onload"in n&&setTimeout(n._sockjs_onload,1)}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./main":14,"./transport-list":16},],2:[function(t,e,n){"use strict";var r=t("inherits"),i=t("./event");function s(){i.call(this),this.initEvent("close",!1,!1),this.wasClean=!1,this.code=0,this.reason=""}r(s,i),e.exports=s},{"./event":4,inherits:54},],3:[function(t,e,n){"use strict";var r=t("inherits"),i=t("./eventtarget");function s(){i.call(this)}r(s,i),s.prototype.removeAllListeners=function(t){t?delete this._listeners[t]:this._listeners={}},s.prototype.once=function(t,e){var n=this,r=!1;this.on(t,function i(){n.removeListener(t,i),r||(r=!0,e.apply(this,arguments))})},s.prototype.emit=function(){var t=arguments[0],e=this._listeners[t];if(e){for(var n=arguments.length,r=Array(n-1),i=1;i<n;i++)r[i-1]=arguments[i];for(var s=0;s<e.length;s++)e[s].apply(this,r)}},s.prototype.on=s.prototype.addListener=i.prototype.addEventListener,s.prototype.removeListener=i.prototype.removeEventListener,e.exports.EventEmitter=s},{"./eventtarget":5,inherits:54},],4:[function(t,e,n){"use strict";function r(t){this.type=t}r.prototype.initEvent=function(t,e,n){return this.type=t,this.bubbles=e,this.cancelable=n,this.timeStamp=+new Date,this},r.prototype.stopPropagation=function(){},r.prototype.preventDefault=function(){},r.CAPTURING_PHASE=1,r.AT_TARGET=2,r.BUBBLING_PHASE=3,e.exports=r},{},],5:[function(t,e,n){"use strict";function r(){this._listeners={}}r.prototype.addEventListener=function(t,e){t in this._listeners||(this._listeners[t]=[]);var n=this._listeners[t];-1===n.indexOf(e)&&(n=n.concat([e])),this._listeners[t]=n},r.prototype.removeEventListener=function(t,e){var n=this._listeners[t];if(n){var r=n.indexOf(e);-1===r||(1<n.length?this._listeners[t]=n.slice(0,r).concat(n.slice(r+1)):delete this._listeners[t])}},r.prototype.dispatchEvent=function(){var t=arguments[0],e=t.type,n=1===arguments.length?[t]:Array.apply(null,arguments);if(this["on"+e]&&this["on"+e].apply(this,n),e in this._listeners)for(var r=this._listeners[e],i=0;i<r.length;i++)r[i].apply(this,n)},e.exports=r},{},],6:[function(t,e,n){"use strict";var r=t("inherits"),i=t("./event");function s(t){i.call(this),this.initEvent("message",!1,!1),this.data=t}r(s,i),e.exports=s},{"./event":4,inherits:54},],7:[function(t,e,n){"use strict";var r=t("./utils/iframe");function i(t){(this._transport=t).on("message",this._transportMessage.bind(this)),t.on("close",this._transportClose.bind(this))}i.prototype._transportClose=function(t,e){r.postMessage("c",JSON.stringify([t,e]))},i.prototype._transportMessage=function(t){r.postMessage("t",t)},i.prototype._send=function(t){this._transport.send(t)},i.prototype._close=function(){this._transport.close(),this._transport.removeAllListeners()},e.exports=i},{"./utils/iframe":47},],8:[function(t,e,n){"use strict";var r=t("./utils/url"),i=t("./utils/event"),s=t("./facade"),o=t("./info-iframe-receiver"),a=t("./utils/iframe"),u=t("./location"),c=function(){};e.exports=function(t,e){var n,l={};e.forEach(function(t){t.facadeTransport&&(l[t.facadeTransport.transportName]=t.facadeTransport)}),l[o.transportName]=o,t.bootstrap_iframe=function(){var e;a.currentWindowId=u.hash.slice(1),i.attachEvent("message",function(i){if(i.source===parent&&(void 0===n&&(n=i.origin),i.origin===n)){var o;try{o=JSON.parse(i.data)}catch(h){return void c("bad json",i.data)}if(o.windowId===a.currentWindowId)switch(o.type){case"s":try{d=JSON.parse(o.data)}catch(f){c("bad json",o.data);break}var d,p=d[0],m=d[1],v=d[2],$=d[3];if(c(p,m,v,$),p!==t.version)throw Error('Incompatible SockJS! Main site uses: "'+p+'", the iframe: "'+t.version+'".');if(!r.isOriginEqual(v,u.href)||!r.isOriginEqual($,u.href))throw Error("Can't connect to different domain from within an iframe. ("+u.href+", "+v+", "+$+")");e=new s(new l[m](v,$));break;case"m":e._send(o.data);break;case"c":e&&e._close(),e=null}}}),a.postMessage("s")}}},{"./facade":7,"./info-iframe-receiver":10,"./location":13,"./utils/event":46,"./utils/iframe":47,"./utils/url":52,debug:void 0},],9:[function(t,e,n){"use strict";var r=t("events").EventEmitter,i=t("inherits"),s=t("./utils/object"),o=function(){};function a(t,e){r.call(this);var n=this,i=+new Date;this.xo=new e("GET",t),this.xo.once("finish",function(t,e){var r,a;if(200===t){if(a=+new Date-i,e)try{r=JSON.parse(e)}catch(u){o("bad json",e)}s.isObject(r)||(r={})}n.emit("finish",r,a),n.removeAllListeners()})}i(a,r),a.prototype.close=function(){this.removeAllListeners(),this.xo.close()},e.exports=a},{"./utils/object":49,debug:void 0,events:3,inherits:54},],10:[function(t,e,n){"use strict";var r=t("inherits"),i=t("events").EventEmitter,s=t("./transport/sender/xhr-local"),o=t("./info-ajax");function a(t){var e=this;i.call(this),this.ir=new o(t,s),this.ir.once("finish",function(t,n){e.ir=null,e.emit("message",JSON.stringify([t,n]))})}r(a,i),a.transportName="iframe-info-receiver",a.prototype.close=function(){this.ir&&(this.ir.close(),this.ir=null),this.removeAllListeners()},e.exports=a},{"./info-ajax":9,"./transport/sender/xhr-local":37,events:3,inherits:54},],11:[function(t,e,n){(function(n){(function(){"use strict";var r=t("events").EventEmitter,i=t("inherits"),s=t("./utils/event"),o=t("./transport/iframe"),a=t("./info-iframe-receiver"),u=function(){};function c(t,e){var i=this;function c(){var n=i.ifr=new o(a.transportName,e,t);n.once("message",function(t){if(t){try{n=JSON.parse(t)}catch(e){return u("bad json",t),i.emit("finish"),void i.close()}var n,r=n[0],s=n[1];i.emit("finish",r,s)}i.close()}),n.once("close",function(){i.emit("finish"),i.close()})}r.call(this),n.document.body?c():s.attachEvent("load",c)}i(c,r),c.enabled=function(){return o.enabled()},c.prototype.close=function(){this.ifr&&this.ifr.close(),this.removeAllListeners(),this.ifr=null},e.exports=c}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./info-iframe-receiver":10,"./transport/iframe":22,"./utils/event":46,debug:void 0,events:3,inherits:54},],12:[function(t,e,n){"use strict";var r=t("events").EventEmitter,i=t("inherits"),s=t("./utils/url"),o=t("./transport/sender/xdr"),a=t("./transport/sender/xhr-cors"),u=t("./transport/sender/xhr-local"),c=t("./transport/sender/xhr-fake"),l=t("./info-iframe"),h=t("./info-ajax"),f=function(){};function d(t,e){f(t);var n=this;r.call(this),setTimeout(function(){n.doXhr(t,e)},0)}i(d,r),d._getReceiver=function(t,e,n){return n.sameOrigin?new h(e,u):a.enabled?new h(e,a):o.enabled&&n.sameScheme?new h(e,o):l.enabled()?new l(t,e):new h(e,c)},d.prototype.doXhr=function(t,e){var n=this,r=s.addPath(t,"/info");f("doXhr",r),this.xo=d._getReceiver(t,r,e),this.timeoutRef=setTimeout(function(){f("timeout"),n._cleanup(!1),n.emit("finish")},d.timeout),this.xo.once("finish",function(t,e){f("finish",t,e),n._cleanup(!0),n.emit("finish",t,e)})},d.prototype._cleanup=function(t){f("_cleanup"),clearTimeout(this.timeoutRef),this.timeoutRef=null,!t&&this.xo&&this.xo.close(),this.xo=null},d.prototype.close=function(){f("close"),this.removeAllListeners(),this._cleanup(!1)},d.timeout=8e3,e.exports=d},{"./info-ajax":9,"./info-iframe":11,"./transport/sender/xdr":34,"./transport/sender/xhr-cors":35,"./transport/sender/xhr-fake":36,"./transport/sender/xhr-local":37,"./utils/url":52,debug:void 0,events:3,inherits:54},],13:[function(t,e,n){(function(t){(function(){"use strict";e.exports=t.location||{origin:"http://localhost:80",protocol:"http:",host:"localhost",port:80,href:"http://localhost/",hash:""}}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{},],14:[function(t,e,n){(function(n){(function(){"use strict";t("./shims");var r,i=t("url-parse"),s=t("inherits"),o=t("./utils/random"),a=t("./utils/escape"),u=t("./utils/url"),c=t("./utils/event"),l=t("./utils/transport"),h=t("./utils/object"),f=t("./utils/browser"),d=t("./utils/log"),p=t("./event/event"),m=t("./event/eventtarget"),v=t("./location"),$=t("./event/close"),b=t("./event/trans-message"),y=t("./info-receiver"),g=function(){};function x(t,e,n){if(!(this instanceof x))return new x(t,e,n);if(arguments.length<1)throw TypeError("Failed to construct 'SockJS: 1 argument required, but only 0 present");m.call(this),this.readyState=x.CONNECTING,this.extensions="",this.protocol="",(n=n||{}).protocols_whitelist&&d.warn("'protocols_whitelist' is DEPRECATED. Use 'transports' instead."),this._transportsWhitelist=n.transports,this._transportOptions=n.transportOptions||{},this._timeout=n.timeout||0;var r=n.sessionId||8;if("function"==typeof r)this._generateSessionId=r;else{if("number"!=typeof r)throw TypeError("If sessionId is used in the options, it needs to be a number or a function.");this._generateSessionId=function(){return o.string(r)}}this._server=n.server||o.numberString(1e3);var s=new i(t);if(!s.host||!s.protocol)throw SyntaxError("The URL '"+t+"' is invalid");if(s.hash)throw SyntaxError("The URL must not contain a fragment");if("http:"!==s.protocol&&"https:"!==s.protocol)throw SyntaxError("The URL's scheme must be either 'http:' or 'https:'. '"+s.protocol+"' is not allowed.");var a="https:"===s.protocol;if("https:"===v.protocol&&!a&&!u.isLoopbackAddr(s.hostname))throw Error("SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS");e?Array.isArray(e)||(e=[e]):e=[];var c=e.sort();c.forEach(function(t,e){if(!t)throw SyntaxError("The protocols entry '"+t+"' is invalid.");if(e<c.length-1&&t===c[e+1])throw SyntaxError("The protocols entry '"+t+"' is duplicated.")});var l=u.getOrigin(v.href);this._origin=l?l.toLowerCase():null,s.set("pathname",s.pathname.replace(/\/+$/,"")),this.url=s.href,g("using url",this.url),this._urlInfo={nullOrigin:!f.hasDomain(),sameOrigin:u.isOriginEqual(this.url,v.href),sameScheme:u.isSchemeEqual(this.url,v.href)},this._ir=new y(this.url,this._urlInfo),this._ir.once("finish",this._receiveInfo.bind(this))}function _(t){return 1e3===t||3e3<=t&&t<=4999}s(x,m),x.prototype.close=function(t,e){if(t&&!_(t))throw Error("InvalidAccessError: Invalid code");if(e&&123<e.length)throw SyntaxError("reason argument has an invalid length");this.readyState!==x.CLOSING&&this.readyState!==x.CLOSED&&this._close(t||1e3,e||"Normal closure",!0)},x.prototype.send=function(t){if("string"!=typeof t&&(t=""+t),this.readyState===x.CONNECTING)throw Error("InvalidStateError: The connection has not been established yet");this.readyState===x.OPEN&&this._transport.send(a.quote(t))},x.version=t("./version"),x.CONNECTING=0,x.OPEN=1,x.CLOSING=2,x.CLOSED=3,x.prototype._receiveInfo=function(t,e){if(g("_receiveInfo",e),this._ir=null,t){this._rto=this.countRTO(e),this._transUrl=t.base_url?t.base_url:this.url,g("info",t=h.extend(t,this._urlInfo));var n=r.filterToEnabled(this._transportsWhitelist,t);this._transports=n.main,g(this._transports.length+" enabled transports"),this._connect()}else this._close(1002,"Cannot connect to server")},x.prototype._connect=function(){for(var t=this._transports.shift();t;t=this._transports.shift()){if(g("attempt",t.transportName),t.needBody&&(!n.document.body||void 0!==n.document.readyState&&"complete"!==n.document.readyState&&"interactive"!==n.document.readyState))return g("waiting for body"),this._transports.unshift(t),void c.attachEvent("load",this._connect.bind(this));var e=Math.max(this._timeout,this._rto*t.roundTrips||5e3);this._transportTimeoutId=setTimeout(this._transportTimeout.bind(this),e),g("using timeout",e);var r=u.addPath(this._transUrl,"/"+this._server+"/"+this._generateSessionId()),i=this._transportOptions[t.transportName];g("transport url",r);var s=new t(r,this._transUrl,i);return s.on("message",this._transportMessage.bind(this)),s.once("close",this._transportClose.bind(this)),s.transportName=t.transportName,void(this._transport=s)}this._close(2e3,"All transports failed",!1)},x.prototype._transportTimeout=function(){g("_transportTimeout"),this.readyState===x.CONNECTING&&(this._transport&&this._transport.close(),this._transportClose(2007,"Transport timed out"))},x.prototype._transportMessage=function(t){g("_transportMessage",t);var e,n=this,r=t.slice(0,1),i=t.slice(1);switch(r){case"o":return void this._open();case"h":return this.dispatchEvent(new p("heartbeat")),void g("heartbeat",this.transport)}if(i)try{e=JSON.parse(i)}catch(s){g("bad json",i)}if(void 0!==e)switch(r){case"a":Array.isArray(e)&&e.forEach(function(t){g("message",n.transport,t),n.dispatchEvent(new b(t))});break;case"m":g("message",this.transport,e),this.dispatchEvent(new b(e));break;case"c":Array.isArray(e)&&2===e.length&&this._close(e[0],e[1],!0)}else g("empty payload",i)},x.prototype._transportClose=function(t,e){g("_transportClose",this.transport,t,e),this._transport&&(this._transport.removeAllListeners(),this._transport=null,this.transport=null),_(t)||2e3===t||this.readyState!==x.CONNECTING?this._close(t,e):this._connect()},x.prototype._open=function(){g("_open",this._transport&&this._transport.transportName,this.readyState),this.readyState===x.CONNECTING?(this._transportTimeoutId&&(clearTimeout(this._transportTimeoutId),this._transportTimeoutId=null),this.readyState=x.OPEN,this.transport=this._transport.transportName,this.dispatchEvent(new p("open")),g("connected",this.transport)):this._close(1006,"Server lost session")},x.prototype._close=function(t,e,n){g("_close",this.transport,t,e,n,this.readyState);var r=!1;if(this._ir&&(r=!0,this._ir.close(),this._ir=null),this._transport&&(this._transport.close(),this._transport=null,this.transport=null),this.readyState===x.CLOSED)throw Error("InvalidStateError: SockJS has already been closed");this.readyState=x.CLOSING,setTimeout((function(){this.readyState=x.CLOSED,r&&this.dispatchEvent(new p("error"));var i=new $("close");i.wasClean=n||!1,i.code=t||1e3,i.reason=e,this.dispatchEvent(i),this.onmessage=this.onclose=this.onerror=null,g("disconnected")}).bind(this),0)},x.prototype.countRTO=function(t){return 100<t?4*t:300+t},e.exports=function(e){return r=l(e),t("./iframe-bootstrap")(x,e),x}}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./event/close":2,"./event/event":4,"./event/eventtarget":5,"./event/trans-message":6,"./iframe-bootstrap":8,"./info-receiver":12,"./location":13,"./shims":15,"./utils/browser":44,"./utils/escape":45,"./utils/event":46,"./utils/log":48,"./utils/object":49,"./utils/random":50,"./utils/transport":51,"./utils/url":52,"./version":53,debug:void 0,inherits:54,"url-parse":57},],15:[function(t,e,n){"use strict";function r(t){return"[object Function]"===a.toString.call(t)}function i(t){return"[object String]"===h.call(t)}var s,o=Array.prototype,a=Object.prototype,u=Function.prototype,c=String.prototype,l=o.slice,h=a.toString;function f(t,e,n){for(var r in e)a.hasOwnProperty.call(e,r)&&s(t,r,e[r],n)}function d(t){if(null==t)throw TypeError("can't convert "+t+" to object");return Object(t)}function p(){}s=Object.defineProperty&&function(){try{return Object.defineProperty({},"x",{}),!0}catch(t){return!1}}()?function(t,e,n,r){!r&&e in t||Object.defineProperty(t,e,{configurable:!0,enumerable:!1,writable:!0,value:n})}:function(t,e,n,r){!r&&e in t||(t[e]=n)},f(u,{bind:function(t){var e=this;if(!r(e))throw TypeError("Function.prototype.bind called on incompatible "+e);for(var n=l.call(arguments,1),i=Math.max(0,e.length-n.length),s=[],o=0;o<i;o++)s.push("$"+o);var a=Function("binder","return function ("+s.join(",")+"){ return binder.apply(this, arguments); }")(function(){if(this instanceof a){var r=e.apply(this,n.concat(l.call(arguments)));return Object(r)===r?r:this}return e.apply(t,n.concat(l.call(arguments)))});return e.prototype&&(p.prototype=e.prototype,a.prototype=new p,p.prototype=null),a}}),f(Array,{isArray:function(t){return"[object Array]"===h.call(t)}});var m,v,$,b=Object("a"),y="a"!==b[0]||!(0 in b);f(o,{forEach:function(t,e){var n=d(this),s=y&&i(this)?this.split(""):n,o=e,a=-1,u=s.length>>>0;if(!r(t))throw TypeError();for(;++a<u;)a in s&&t.call(o,s[a],a,n)}},(m=o.forEach,$=v=!0,m&&(m.call("foo",function(t,e,n){"object"!=typeof n&&(v=!1)}),m.call([1],function(){$="string"==typeof this},"x")),!(m&&v&&$))),f(o,{indexOf:function(t,e){var n=y&&i(this)?this.split(""):d(this),r=n.length>>>0;if(!r)return -1;var s,o,a=0;for(1<arguments.length&&(a=((o=+(s=e))!=o?o=0:0!==o&&o!==1/0&&o!==-1/0&&(o=(0<o||-1)*Math.floor(Math.abs(o))),o)),a=0<=a?a:Math.max(0,r+a);a<r;a++)if(a in n&&n[a]===t)return a;return -1}},Array.prototype.indexOf&&-1!==[0,1].indexOf(1,2));var g,x=c.split;2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||"t"==="tesst".split(/(s)*/)[1]||4!=="test".split(/(?:)/,-1).length||"".split(/.?/).length||1<".".split(/()()/).length?(g=void 0===/()??/.exec("")[1],c.split=function(t,e){var n=this;if(void 0===t&&0===e)return[];if("[object RegExp]"!==h.call(t))return x.call(this,t,e);var r,i,s,a,u,c=[],l=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.extended?"x":"")+(t.sticky?"y":""),f=0;for(t=RegExp(t.source,l+"g"),n+="",g||(r=RegExp("^"+t.source+"$(?!\\s)",l)),e=void 0===e?4294967295:(u=e)>>>0;(i=t.exec(n))&&!(f<(s=i.index+i[0].length)&&(c.push(n.slice(f,i.index)),!g&&1<i.length&&i[0].replace(r,function(){for(var t=1;t<arguments.length-2;t++)void 0===arguments[t]&&(i[t]=void 0)}),1<i.length&&i.index<n.length&&o.push.apply(c,i.slice(1)),a=i[0].length,f=s,c.length>=e));)t.lastIndex===i.index&&t.lastIndex++;return f===n.length?!a&&t.test("")||c.push(""):c.push(n.slice(f)),c.length>e?c.slice(0,e):c}):"0".split(void 0,0).length&&(c.split=function(t,e){return void 0===t&&0===e?[]:x.call(this,t,e)});var _=c.substr;f(c,{substr:function(t,e){return _.call(this,t<0&&(t=this.length+t)<0?0:t,e)}},"".substr&&"b"!=="0b".substr(-1))},{},],16:[function(t,e,n){"use strict";e.exports=[t("./transport/websocket"),t("./transport/xhr-streaming"),t("./transport/xdr-streaming"),t("./transport/eventsource"),t("./transport/lib/iframe-wrap")(t("./transport/eventsource")),t("./transport/htmlfile"),t("./transport/lib/iframe-wrap")(t("./transport/htmlfile")),t("./transport/xhr-polling"),t("./transport/xdr-polling"),t("./transport/lib/iframe-wrap")(t("./transport/xhr-polling")),t("./transport/jsonp-polling"),]},{"./transport/eventsource":20,"./transport/htmlfile":21,"./transport/jsonp-polling":23,"./transport/lib/iframe-wrap":26,"./transport/websocket":38,"./transport/xdr-polling":39,"./transport/xdr-streaming":40,"./transport/xhr-polling":41,"./transport/xhr-streaming":42},],17:[function(t,e,n){(function(n){(function(){"use strict";var r=t("events").EventEmitter,i=t("inherits"),s=t("../../utils/event"),o=t("../../utils/url"),a=n.XMLHttpRequest,u=function(){};function c(t,e,n,i){u(t,e);var s=this;r.call(this),setTimeout(function(){s._start(t,e,n,i)},0)}i(c,r),c.prototype._start=function(t,e,n,r){var i=this;try{this.xhr=new a}catch(l){}if(!this.xhr)return u("no xhr"),this.emit("finish",0,"no xhr support"),void this._cleanup();e=o.addQuery(e,"t="+ +new Date),this.unloadRef=s.unloadAdd(function(){u("unload cleanup"),i._cleanup(!0)});try{this.xhr.open(t,e,!0),this.timeout&&"timeout"in this.xhr&&(this.xhr.timeout=this.timeout,this.xhr.ontimeout=function(){u("xhr timeout"),i.emit("finish",0,""),i._cleanup(!1)})}catch(h){return u("exception",h),this.emit("finish",0,""),void this._cleanup(!1)}if(r&&r.noCredentials||!c.supportsCORS||(u("withCredentials"),this.xhr.withCredentials=!0),r&&r.headers)for(var f in r.headers)this.xhr.setRequestHeader(f,r.headers[f]);this.xhr.onreadystatechange=function(){if(i.xhr){var t,e,n=i.xhr;switch(u("readyState",n.readyState),n.readyState){case 3:try{e=n.status,t=n.responseText}catch(r){}u("status",e),1223===e&&(e=204),200===e&&t&&0<t.length&&(u("chunk"),i.emit("chunk",e,t));break;case 4:u("status",e=n.status),1223===e&&(e=204),12005!==e&&12029!==e||(e=0),u("finish",e,n.responseText),i.emit("finish",e,n.responseText),i._cleanup(!1)}}};try{i.xhr.send(n)}catch(d){i.emit("finish",0,""),i._cleanup(!1)}},c.prototype._cleanup=function(t){if(u("cleanup"),this.xhr){if(this.removeAllListeners(),s.unloadDel(this.unloadRef),this.xhr.onreadystatechange=function(){},this.xhr.ontimeout&&(this.xhr.ontimeout=null),t)try{this.xhr.abort()}catch(e){}this.unloadRef=this.xhr=null}},c.prototype.close=function(){u("close"),this._cleanup(!0)},c.enabled=!!a;var l=["Active"].concat("Object").join("X");!c.enabled&&l in n&&(u("overriding xmlhttprequest"),c.enabled=(new(a=function(){try{return new n[l]("Microsoft.XMLHTTP")}catch(t){return null}}),!0));var h=!1;try{h="withCredentials"in new a}catch(f){}c.supportsCORS=h,e.exports=c}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"../../utils/event":46,"../../utils/url":52,debug:void 0,events:3,inherits:54},],18:[function(t,e,n){(function(t){(function(){e.exports=t.EventSource}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{},],19:[function(t,e,n){(function(t){(function(){"use strict";var n=t.WebSocket||t.MozWebSocket;e.exports=n?function(t){return new n(t)}:void 0}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{},],20:[function(t,e,n){"use strict";var r=t("inherits"),i=t("./lib/ajax-based"),s=t("./receiver/eventsource"),o=t("./sender/xhr-cors"),a=t("eventsource");function u(t){if(!u.enabled())throw Error("Transport created when disabled");i.call(this,t,"/eventsource",s,o)}r(u,i),u.enabled=function(){return!!a},u.transportName="eventsource",u.roundTrips=2,e.exports=u},{"./lib/ajax-based":24,"./receiver/eventsource":29,"./sender/xhr-cors":35,eventsource:18,inherits:54},],21:[function(t,e,n){"use strict";var r=t("inherits"),i=t("./receiver/htmlfile"),s=t("./sender/xhr-local"),o=t("./lib/ajax-based");function a(t){if(!i.enabled)throw Error("Transport created when disabled");o.call(this,t,"/htmlfile",i,s)}r(a,o),a.enabled=function(t){return i.enabled&&t.sameOrigin},a.transportName="htmlfile",a.roundTrips=2,e.exports=a},{"./lib/ajax-based":24,"./receiver/htmlfile":30,"./sender/xhr-local":37,inherits:54},],22:[function(t,e,n){"use strict";var r=t("inherits"),i=t("events").EventEmitter,s=t("../version"),o=t("../utils/url"),a=t("../utils/iframe"),u=t("../utils/event"),c=t("../utils/random"),l=function(){};function h(t,e,n){if(!h.enabled())throw Error("Transport created when disabled");i.call(this);var r=this;this.origin=o.getOrigin(n),this.baseUrl=n,this.transUrl=e,this.transport=t,this.windowId=c.string(8);var s=o.addPath(n,"/iframe.html")+"#"+this.windowId;l(t,e,s),this.iframeObj=a.createIframe(s,function(t){l("err callback"),r.emit("close",1006,"Unable to load an iframe ("+t+")"),r.close()}),this.onmessageCallback=this._message.bind(this),u.attachEvent("message",this.onmessageCallback)}r(h,i),h.prototype.close=function(){if(l("close"),this.removeAllListeners(),this.iframeObj){u.detachEvent("message",this.onmessageCallback);try{this.postMessage("c")}catch(t){}this.iframeObj.cleanup(),this.iframeObj=null,this.onmessageCallback=this.iframeObj=null}},h.prototype._message=function(t){if(l("message",t.data),o.isOriginEqual(t.origin,this.origin)){var e,n;try{e=JSON.parse(t.data)}catch(r){return void l("bad json",t.data)}if(e.windowId===this.windowId)switch(e.type){case"s":this.iframeObj.loaded(),this.postMessage("s",JSON.stringify([s,this.transport,this.transUrl,this.baseUrl,]));break;case"t":this.emit("message",e.data);break;case"c":try{n=JSON.parse(e.data)}catch(i){return void l("bad json",e.data)}this.emit("close",n[0],n[1]),this.close()}else l("mismatched window id",e.windowId,this.windowId)}else l("not same origin",t.origin,this.origin)},h.prototype.postMessage=function(t,e){l("postMessage",t,e),this.iframeObj.post(JSON.stringify({windowId:this.windowId,type:t,data:e||""}),this.origin)},h.prototype.send=function(t){l("send",t),this.postMessage("m",t)},h.enabled=function(){return a.iframeEnabled},h.transportName="iframe",h.roundTrips=2,e.exports=h},{"../utils/event":46,"../utils/iframe":47,"../utils/random":50,"../utils/url":52,"../version":53,debug:void 0,events:3,inherits:54},],23:[function(t,e,n){(function(n){(function(){"use strict";var r=t("inherits"),i=t("./lib/sender-receiver"),s=t("./receiver/jsonp"),o=t("./sender/jsonp");function a(t){if(!a.enabled())throw Error("Transport created when disabled");i.call(this,t,"/jsonp",o,s)}r(a,i),a.enabled=function(){return!!n.document},a.transportName="jsonp-polling",a.roundTrips=1,a.needBody=!0,e.exports=a}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./lib/sender-receiver":28,"./receiver/jsonp":31,"./sender/jsonp":33,inherits:54},],24:[function(t,e,n){"use strict";var r=t("inherits"),i=t("../../utils/url"),s=t("./sender-receiver"),o=function(){};function a(t,e,n,r){var a;s.call(this,t,e,(a=r,function(t,e,n){o("create ajax sender",t,e);var r={};"string"==typeof e&&(r.headers={"Content-type":"text/plain"});var s=i.addPath(t,"/xhr_send"),u=new a("POST",s,e,r);return u.once("finish",function(t){if(o("finish",t),u=null,200!==t&&204!==t)return n(Error("http status "+t));n()}),function(){o("abort"),u.close(),u=null;var t=Error("Aborted");t.code=1e3,n(t)}}),n,r)}r(a,s),e.exports=a},{"../../utils/url":52,"./sender-receiver":28,debug:void 0,inherits:54},],25:[function(t,e,n){"use strict";var r=t("inherits"),i=t("events").EventEmitter,s=function(){};function o(t,e){s(t),i.call(this),this.sendBuffer=[],this.sender=e,this.url=t}r(o,i),o.prototype.send=function(t){s("send",t),this.sendBuffer.push(t),this.sendStop||this.sendSchedule()},o.prototype.sendScheduleWait=function(){s("sendScheduleWait");var t,e=this;this.sendStop=function(){s("sendStop"),e.sendStop=null,clearTimeout(t)},t=setTimeout(function(){s("timeout"),e.sendStop=null,e.sendSchedule()},25)},o.prototype.sendSchedule=function(){s("sendSchedule",this.sendBuffer.length);var t=this;if(0<this.sendBuffer.length){var e="["+this.sendBuffer.join(",")+"]";this.sendStop=this.sender(this.url,e,function(e){t.sendStop=null,e?(s("error",e),t.emit("close",e.code||1006,"Sending error: "+e),t.close()):t.sendScheduleWait()}),this.sendBuffer=[]}},o.prototype._cleanup=function(){s("_cleanup"),this.removeAllListeners()},o.prototype.close=function(){s("close"),this._cleanup(),this.sendStop&&(this.sendStop(),this.sendStop=null)},e.exports=o},{debug:void 0,events:3,inherits:54},],26:[function(t,e,n){(function(n){(function(){"use strict";var r=t("inherits"),i=t("../iframe"),s=t("../../utils/object");e.exports=function(t){function e(e,n){i.call(this,t.transportName,e,n)}return r(e,i),e.enabled=function(e,r){if(!n.document)return!1;var o=s.extend({},r);return o.sameOrigin=!0,t.enabled(o)&&i.enabled()},e.transportName="iframe-"+t.transportName,e.needBody=!0,e.roundTrips=i.roundTrips+t.roundTrips-1,e.facadeTransport=t,e}}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"../../utils/object":49,"../iframe":22,inherits:54},],27:[function(t,e,n){"use strict";var r=t("inherits"),i=t("events").EventEmitter,s=function(){};function o(t,e,n){s(e),i.call(this),this.Receiver=t,this.receiveUrl=e,this.AjaxObject=n,this._scheduleReceiver()}r(o,i),o.prototype._scheduleReceiver=function(){s("_scheduleReceiver");var t=this,e=this.poll=new this.Receiver(this.receiveUrl,this.AjaxObject);e.on("message",function(e){s("message",e),t.emit("message",e)}),e.once("close",function(n,r){s("close",n,r,t.pollIsClosing),t.poll=e=null,t.pollIsClosing||("network"===r?t._scheduleReceiver():(t.emit("close",n||1006,r),t.removeAllListeners()))})},o.prototype.abort=function(){s("abort"),this.removeAllListeners(),this.pollIsClosing=!0,this.poll&&this.poll.abort()},e.exports=o},{debug:void 0,events:3,inherits:54},],28:[function(t,e,n){"use strict";var r=t("inherits"),i=t("../../utils/url"),s=t("./buffered-sender"),o=t("./polling"),a=function(){};function u(t,e,n,r,u){var c=i.addPath(t,e);a(c);var l=this;s.call(this,t,n),this.poll=new o(r,c,u),this.poll.on("message",function(t){a("poll message",t),l.emit("message",t)}),this.poll.once("close",function(t,e){a("poll close",t,e),l.poll=null,l.emit("close",t,e),l.close()})}r(u,s),u.prototype.close=function(){s.prototype.close.call(this),a("close"),this.removeAllListeners(),this.poll&&(this.poll.abort(),this.poll=null)},e.exports=u},{"../../utils/url":52,"./buffered-sender":25,"./polling":27,debug:void 0,inherits:54},],29:[function(t,e,n){"use strict";var r=t("inherits"),i=t("events").EventEmitter,s=t("eventsource"),o=function(){};function a(t){o(t),i.call(this);var e=this,n=this.es=new s(t);n.onmessage=function(t){o("message",t.data),e.emit("message",decodeURI(t.data))},n.onerror=function(t){o("error",n.readyState,t);var r=2!==n.readyState?"network":"permanent";e._cleanup(),e._close(r)}}r(a,i),a.prototype.abort=function(){o("abort"),this._cleanup(),this._close("user")},a.prototype._cleanup=function(){o("cleanup");var t=this.es;t&&(t.onmessage=t.onerror=null,t.close(),this.es=null)},a.prototype._close=function(t){o("close",t);var e=this;setTimeout(function(){e.emit("close",null,t),e.removeAllListeners()},200)},e.exports=a},{debug:void 0,events:3,eventsource:18,inherits:54},],30:[function(t,e,n){(function(n){(function(){"use strict";var r=t("inherits"),i=t("../../utils/iframe"),s=t("../../utils/url"),o=t("events").EventEmitter,a=t("../../utils/random"),u=function(){};function c(t){u(t),o.call(this);var e=this;i.polluteGlobalNamespace(),this.id="a"+a.string(6),t=s.addQuery(t,"c="+decodeURIComponent(i.WPrefix+"."+this.id)),u("using htmlfile",c.htmlfileEnabled);var r=c.htmlfileEnabled?i.createHtmlfile:i.createIframe;n[i.WPrefix][this.id]={start:function(){u("start"),e.iframeObj.loaded()},message:function(t){u("message",t),e.emit("message",t)},stop:function(){u("stop"),e._cleanup(),e._close("network")}},this.iframeObj=r(t,function(){u("callback"),e._cleanup(),e._close("permanent")})}r(c,o),c.prototype.abort=function(){u("abort"),this._cleanup(),this._close("user")},c.prototype._cleanup=function(){u("_cleanup"),this.iframeObj&&(this.iframeObj.cleanup(),this.iframeObj=null),delete n[i.WPrefix][this.id]},c.prototype._close=function(t){u("_close",t),this.emit("close",null,t),this.removeAllListeners()},c.htmlfileEnabled=!1;var l=["Active"].concat("Object").join("X");if(l in n)try{c.htmlfileEnabled=(new n[l]("htmlfile"),!0)}catch(h){}c.enabled=c.htmlfileEnabled||i.iframeEnabled,e.exports=c}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"../../utils/iframe":47,"../../utils/random":50,"../../utils/url":52,debug:void 0,events:3,inherits:54},],31:[function(t,e,n){(function(n){(function(){"use strict";var r=t("../../utils/iframe"),i=t("../../utils/random"),s=t("../../utils/browser"),o=t("../../utils/url"),a=t("inherits"),u=t("events").EventEmitter,c=function(){};function l(t){c(t);var e=this;u.call(this),r.polluteGlobalNamespace(),this.id="a"+i.string(6);var s=o.addQuery(t,"c="+encodeURIComponent(r.WPrefix+"."+this.id));n[r.WPrefix][this.id]=this._callback.bind(this),this._createScript(s),this.timeoutId=setTimeout(function(){c("timeout"),e._abort(Error("JSONP script loaded abnormally (timeout)"))},l.timeout)}a(l,u),l.prototype.abort=function(){if(c("abort"),n[r.WPrefix][this.id]){var t=Error("JSONP user aborted read");t.code=1e3,this._abort(t)}},l.timeout=35e3,l.scriptErrorTimeout=1e3,l.prototype._callback=function(t){c("_callback",t),this._cleanup(),this.aborting||(t&&(c("message",t),this.emit("message",t)),this.emit("close",null,"network"),this.removeAllListeners())},l.prototype._abort=function(t){c("_abort",t),this._cleanup(),this.aborting=!0,this.emit("close",t.code,t.message),this.removeAllListeners()},l.prototype._cleanup=function(){if(c("_cleanup"),clearTimeout(this.timeoutId),this.script2&&(this.script2.parentNode.removeChild(this.script2),this.script2=null),this.script){var t=this.script;t.parentNode.removeChild(t),t.onreadystatechange=t.onerror=t.onload=t.onclick=null,this.script=null}delete n[r.WPrefix][this.id]},l.prototype._scriptError=function(){c("_scriptError");var t=this;this.errorTimer||(this.errorTimer=setTimeout(function(){t.loadedOkay||t._abort(Error("JSONP script loaded abnormally (onerror)"))},l.scriptErrorTimeout))},l.prototype._createScript=function(t){c("_createScript",t);var e,r=this,o=this.script=n.document.createElement("script");if(o.id="a"+i.string(8),o.src=t,o.type="text/javascript",o.charset="UTF-8",o.onerror=this._scriptError.bind(this),o.onload=function(){c("onload"),r._abort(Error("JSONP script loaded abnormally (onload)"))},o.onreadystatechange=function(){if(c("onreadystatechange",o.readyState),/loaded|closed/.test(o.readyState)){if(o&&o.htmlFor&&o.onclick){r.loadedOkay=!0;try{o.onclick()}catch(t){}}o&&r._abort(Error("JSONP script loaded abnormally (onreadystatechange)"))}},void 0===o.async&&n.document.attachEvent){if(s.isOpera())(e=this.script2=n.document.createElement("script")).text="try{var a = document.getElementById('"+o.id+"'); if(a)a.onerror();}catch(x){};",o.async=e.async=!1;else{try{o.htmlFor=o.id,o.event="onclick"}catch(a){}o.async=!0}}void 0!==o.async&&(o.async=!0);var u=n.document.getElementsByTagName("head")[0];u.insertBefore(o,u.firstChild),e&&u.insertBefore(e,u.firstChild)},e.exports=l}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"../../utils/browser":44,"../../utils/iframe":47,"../../utils/random":50,"../../utils/url":52,debug:void 0,events:3,inherits:54},],32:[function(t,e,n){"use strict";var r=t("inherits"),i=t("events").EventEmitter,s=function(){};function o(t,e){s(t),i.call(this);var n=this;this.bufferPosition=0,this.xo=new e("POST",t,null),this.xo.on("chunk",this._chunkHandler.bind(this)),this.xo.once("finish",function(t,e){s("finish",t,e),n._chunkHandler(t,e),n.xo=null;var r=200===t?"network":"permanent";s("close",r),n.emit("close",null,r),n._cleanup()})}r(o,i),o.prototype._chunkHandler=function(t,e){if(s("_chunkHandler",t),200===t&&e)for(var n=-1;;this.bufferPosition+=n+1){var r=e.slice(this.bufferPosition);if(-1===(n=r.indexOf("\n")))break;var i=r.slice(0,n);i&&(s("message",i),this.emit("message",i))}},o.prototype._cleanup=function(){s("_cleanup"),this.removeAllListeners()},o.prototype.abort=function(){s("abort"),this.xo&&(this.xo.close(),s("close"),this.emit("close",null,"user"),this.xo=null),this._cleanup()},e.exports=o},{debug:void 0,events:3,inherits:54},],33:[function(t,e,n){(function(n){(function(){"use strict";var r,i,s=t("../../utils/random"),o=t("../../utils/url"),a=function(){};e.exports=function(t,e,u){a(t,e),r||(a("createForm"),(r=n.document.createElement("form")).style.display="none",r.style.position="absolute",r.method="POST",r.enctype="application/x-www-form-urlencoded",r.acceptCharset="UTF-8",(i=n.document.createElement("textarea")).name="d",r.appendChild(i),n.document.body.appendChild(r));var c="a"+s.string(8);r.target=c,r.action=o.addQuery(o.addPath(t,"/jsonp_send"),"i="+c);var l=function(t){a("createIframe",t);try{return n.document.createElement('<iframe name="'+t+'">')}catch(e){var r=n.document.createElement("iframe");return r.name=t,r}}(c);l.id=c,l.style.display="none",r.appendChild(l);try{i.value=e}catch(h){}function f(t){a("completed",c,t),l.onerror&&(l.onreadystatechange=l.onerror=l.onload=null,setTimeout(function(){a("cleaning up",c),l.parentNode.removeChild(l),l=null},500),i.value="",u(t))}return r.submit(),l.onerror=function(){a("onerror",c),f()},l.onload=function(){a("onload",c),f()},l.onreadystatechange=function(t){a("onreadystatechange",c,l.readyState,t),"complete"===l.readyState&&f()},function(){a("aborted",c),f(Error("Aborted"))}}}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"../../utils/random":50,"../../utils/url":52,debug:void 0},],34:[function(t,e,n){(function(n){(function(){"use strict";var r=t("events").EventEmitter,i=t("inherits"),s=t("../../utils/event"),o=t("../../utils/browser"),a=t("../../utils/url"),u=function(){};function c(t,e,n){u(t,e);var i=this;r.call(this),setTimeout(function(){i._start(t,e,n)},0)}i(c,r),c.prototype._start=function(t,e,r){u("_start");var i=this,o=new n.XDomainRequest;e=a.addQuery(e,"t="+ +new Date),o.onerror=function(){u("onerror"),i._error()},o.ontimeout=function(){u("ontimeout"),i._error()},o.onprogress=function(){u("progress",o.responseText),i.emit("chunk",200,o.responseText)},o.onload=function(){u("load"),i.emit("finish",200,o.responseText),i._cleanup(!1)},this.xdr=o,this.unloadRef=s.unloadAdd(function(){i._cleanup(!0)});try{this.xdr.open(t,e),this.timeout&&(this.xdr.timeout=this.timeout),this.xdr.send(r)}catch(c){this._error()}},c.prototype._error=function(){this.emit("finish",0,""),this._cleanup(!1)},c.prototype._cleanup=function(t){if(u("cleanup",t),this.xdr){if(this.removeAllListeners(),s.unloadDel(this.unloadRef),this.xdr.ontimeout=this.xdr.onerror=this.xdr.onprogress=this.xdr.onload=null,t)try{this.xdr.abort()}catch(e){}this.unloadRef=this.xdr=null}},c.prototype.close=function(){u("close"),this._cleanup(!0)},c.enabled=!(!n.XDomainRequest||!o.hasDomain()),e.exports=c}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"../../utils/browser":44,"../../utils/event":46,"../../utils/url":52,debug:void 0,events:3,inherits:54},],35:[function(t,e,n){"use strict";var r=t("inherits"),i=t("../driver/xhr");function s(t,e,n,r){i.call(this,t,e,n,r)}r(s,i),s.enabled=i.enabled&&i.supportsCORS,e.exports=s},{"../driver/xhr":17,inherits:54},],36:[function(t,e,n){"use strict";var r=t("events").EventEmitter;function i(){var t=this;r.call(this),this.to=setTimeout(function(){t.emit("finish",200,"{}")},i.timeout)}t("inherits")(i,r),i.prototype.close=function(){clearTimeout(this.to)},i.timeout=2e3,e.exports=i},{events:3,inherits:54},],37:[function(t,e,n){"use strict";var r=t("inherits"),i=t("../driver/xhr");function s(t,e,n){i.call(this,t,e,n,{noCredentials:!0})}r(s,i),s.enabled=i.enabled,e.exports=s},{"../driver/xhr":17,inherits:54},],38:[function(t,e,n){"use strict";var r=t("../utils/event"),i=t("../utils/url"),s=t("inherits"),o=t("events").EventEmitter,a=t("./driver/websocket"),u=function(){};function c(t,e,n){if(!c.enabled())throw Error("Transport created when disabled");o.call(this),u("constructor",t);var s=this,l=i.addPath(t,"/websocket");l="https"===l.slice(0,5)?"wss"+l.slice(5):"ws"+l.slice(4),this.url=l,this.ws=new a(this.url,[],n),this.ws.onmessage=function(t){u("message event",t.data),s.emit("message",t.data)},this.unloadRef=r.unloadAdd(function(){u("unload"),s.ws.close()}),this.ws.onclose=function(t){u("close event",t.code,t.reason),s.emit("close",t.code,t.reason),s._cleanup()},this.ws.onerror=function(t){u("error event",t),s.emit("close",1006,"WebSocket connection broken"),s._cleanup()}}s(c,o),c.prototype.send=function(t){var e="["+t+"]";u("send",e),this.ws.send(e)},c.prototype.close=function(){u("close");var t=this.ws;this._cleanup(),t&&t.close()},c.prototype._cleanup=function(){u("_cleanup");var t=this.ws;t&&(t.onmessage=t.onclose=t.onerror=null),r.unloadDel(this.unloadRef),this.unloadRef=this.ws=null,this.removeAllListeners()},c.enabled=function(){return u("enabled"),!!a},c.transportName="websocket",c.roundTrips=2,e.exports=c},{"../utils/event":46,"../utils/url":52,"./driver/websocket":19,debug:void 0,events:3,inherits:54},],39:[function(t,e,n){"use strict";var r=t("inherits"),i=t("./lib/ajax-based"),s=t("./xdr-streaming"),o=t("./receiver/xhr"),a=t("./sender/xdr");function u(t){if(!a.enabled)throw Error("Transport created when disabled");i.call(this,t,"/xhr",o,a)}r(u,i),u.enabled=s.enabled,u.transportName="xdr-polling",u.roundTrips=2,e.exports=u},{"./lib/ajax-based":24,"./receiver/xhr":32,"./sender/xdr":34,"./xdr-streaming":40,inherits:54},],40:[function(t,e,n){"use strict";var r=t("inherits"),i=t("./lib/ajax-based"),s=t("./receiver/xhr"),o=t("./sender/xdr");function a(t){if(!o.enabled)throw Error("Transport created when disabled");i.call(this,t,"/xhr_streaming",s,o)}r(a,i),a.enabled=function(t){return!t.cookie_needed&&!t.nullOrigin&&o.enabled&&t.sameScheme},a.transportName="xdr-streaming",a.roundTrips=2,e.exports=a},{"./lib/ajax-based":24,"./receiver/xhr":32,"./sender/xdr":34,inherits:54},],41:[function(t,e,n){"use strict";var r=t("inherits"),i=t("./lib/ajax-based"),s=t("./receiver/xhr"),o=t("./sender/xhr-cors"),a=t("./sender/xhr-local");function u(t){if(!a.enabled&&!o.enabled)throw Error("Transport created when disabled");i.call(this,t,"/xhr",s,o)}r(u,i),u.enabled=function(t){return!t.nullOrigin&&(!(!a.enabled||!t.sameOrigin)||o.enabled)},u.transportName="xhr-polling",u.roundTrips=2,e.exports=u},{"./lib/ajax-based":24,"./receiver/xhr":32,"./sender/xhr-cors":35,"./sender/xhr-local":37,inherits:54},],42:[function(t,e,n){(function(n){(function(){"use strict";var r=t("inherits"),i=t("./lib/ajax-based"),s=t("./receiver/xhr"),o=t("./sender/xhr-cors"),a=t("./sender/xhr-local"),u=t("../utils/browser");function c(t){if(!a.enabled&&!o.enabled)throw Error("Transport created when disabled");i.call(this,t,"/xhr_streaming",s,o)}r(c,i),c.enabled=function(t){return!t.nullOrigin&&!u.isOpera()&&o.enabled},c.transportName="xhr-streaming",c.roundTrips=2,c.needBody=!!n.document,e.exports=c}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"../utils/browser":44,"./lib/ajax-based":24,"./receiver/xhr":32,"./sender/xhr-cors":35,"./sender/xhr-local":37,inherits:54},],43:[function(t,e,n){(function(t){(function(){"use strict";t.crypto&&t.crypto.getRandomValues?e.exports.randomBytes=function(e){var n=new Uint8Array(e);return t.crypto.getRandomValues(n),n}:e.exports.randomBytes=function(t){for(var e=Array(t),n=0;n<t;n++)e[n]=Math.floor(256*Math.random());return e}}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{},],44:[function(t,e,n){(function(t){(function(){"use strict";e.exports={isOpera:function(){return t.navigator&&/opera/i.test(t.navigator.userAgent)},isKonqueror:function(){return t.navigator&&/konqueror/i.test(t.navigator.userAgent)},hasDomain:function(){if(!t.document)return!0;try{return!!t.document.domain}catch(e){return!1}}}}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{},],45:[function(t,e,n){"use strict";var r,i=/[\x00-\x1f\ud800-\udfff\ufffe\uffff\u0300-\u0333\u033d-\u0346\u034a-\u034c\u0350-\u0352\u0357-\u0358\u035c-\u0362\u0374\u037e\u0387\u0591-\u05af\u05c4\u0610-\u0617\u0653-\u0654\u0657-\u065b\u065d-\u065e\u06df-\u06e2\u06eb-\u06ec\u0730\u0732-\u0733\u0735-\u0736\u073a\u073d\u073f-\u0741\u0743\u0745\u0747\u07eb-\u07f1\u0951\u0958-\u095f\u09dc-\u09dd\u09df\u0a33\u0a36\u0a59-\u0a5b\u0a5e\u0b5c-\u0b5d\u0e38-\u0e39\u0f43\u0f4d\u0f52\u0f57\u0f5c\u0f69\u0f72-\u0f76\u0f78\u0f80-\u0f83\u0f93\u0f9d\u0fa2\u0fa7\u0fac\u0fb9\u1939-\u193a\u1a17\u1b6b\u1cda-\u1cdb\u1dc0-\u1dcf\u1dfc\u1dfe\u1f71\u1f73\u1f75\u1f77\u1f79\u1f7b\u1f7d\u1fbb\u1fbe\u1fc9\u1fcb\u1fd3\u1fdb\u1fe3\u1feb\u1fee-\u1fef\u1ff9\u1ffb\u1ffd\u2000-\u2001\u20d0-\u20d1\u20d4-\u20d7\u20e7-\u20e9\u2126\u212a-\u212b\u2329-\u232a\u2adc\u302b-\u302c\uaab2-\uaab3\uf900-\ufa0d\ufa10\ufa12\ufa15-\ufa1e\ufa20\ufa22\ufa25-\ufa26\ufa2a-\ufa2d\ufa30-\ufa6d\ufa70-\ufad9\ufb1d\ufb1f\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufb4e\ufff0-\uffff]/g;e.exports={quote:function(t){var e=JSON.stringify(t);return i.lastIndex=0,i.test(e)?(r=r||function(t){var e,n={},r=[];for(e=0;e<65536;e++)r.push(String.fromCharCode(e));return t.lastIndex=0,r.join("").replace(t,function(t){return n[t]="\\u"+("0000"+t.charCodeAt(0).toString(16)).slice(-4),""}),t.lastIndex=0,n}(i),e.replace(i,function(t){return r[t]})):e}}},{},],46:[function(t,e,n){(function(n){(function(){"use strict";var r=t("./random"),i={},s=!1,o=n.chrome&&n.chrome.app&&n.chrome.app.runtime;e.exports={attachEvent:function(t,e){void 0!==n.addEventListener?n.addEventListener(t,e,!1):n.document&&n.attachEvent&&(n.document.attachEvent("on"+t,e),n.attachEvent("on"+t,e))},detachEvent:function(t,e){void 0!==n.addEventListener?n.removeEventListener(t,e,!1):n.document&&n.detachEvent&&(n.document.detachEvent("on"+t,e),n.detachEvent("on"+t,e))},unloadAdd:function(t){if(o)return null;var e=r.string(8);return i[e]=t,s&&setTimeout(this.triggerUnloadCallbacks,0),e},unloadDel:function(t){t in i&&delete i[t]},triggerUnloadCallbacks:function(){for(var t in i)i[t](),delete i[t]}},o||e.exports.attachEvent("unload",function(){s||(s=!0,e.exports.triggerUnloadCallbacks())})}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./random":50},],47:[function(t,e,n){(function(n){(function(){"use strict";var r=t("./event"),i=t("./browser"),s=function(){};e.exports={WPrefix:"_jp",currentWindowId:null,polluteGlobalNamespace:function(){e.exports.WPrefix in n||(n[e.exports.WPrefix]={})},postMessage:function(t,r){n.parent!==n?n.parent.postMessage(JSON.stringify({windowId:e.exports.currentWindowId,type:t,data:r||""}),"*"):s("Cannot postMessage, no parent window.",t,r)},createIframe:function(t,e){function i(){s("unattach"),clearTimeout(u);try{l.onload=null}catch(t){}l.onerror=null}function o(){s("cleanup"),l&&(i(),setTimeout(function(){l&&l.parentNode.removeChild(l),l=null},0),r.unloadDel(c))}function a(t){s("onerror",t),l&&(o(),e(t))}var u,c,l=n.document.createElement("iframe");return l.src=t,l.style.display="none",l.style.position="absolute",l.onerror=function(){a("onerror")},l.onload=function(){s("onload"),clearTimeout(u),u=setTimeout(function(){a("onload timeout")},2e3)},n.document.body.appendChild(l),u=setTimeout(function(){a("timeout")},15e3),c=r.unloadAdd(o),{post:function(t,e){s("post",t,e),setTimeout(function(){try{l&&l.contentWindow&&l.contentWindow.postMessage(t,e)}catch(n){}},0)},cleanup:o,loaded:i}},createHtmlfile:function(t,i){function o(){clearTimeout(c),h.onerror=null}function a(){f&&(o(),r.unloadDel(l),h.parentNode.removeChild(h),h=f=null,CollectGarbage())}function u(t){s("onerror",t),f&&(a(),i(t))}var c,l,h,f=new n[["Active"].concat("Object").join("X")]("htmlfile");f.open(),f.write('<html><script>document.domain="'+n.document.domain+'";</script></html>'),f.close(),f.parentWindow[e.exports.WPrefix]=n[e.exports.WPrefix];var d=f.createElement("div");return f.body.appendChild(d),h=f.createElement("iframe"),d.appendChild(h),h.src=t,h.onerror=function(){u("onerror")},c=setTimeout(function(){u("timeout")},15e3),l=r.unloadAdd(a),{post:function(t,e){try{setTimeout(function(){h&&h.contentWindow&&h.contentWindow.postMessage(t,e)},0)}catch(n){}},cleanup:a,loaded:o}}},e.exports.iframeEnabled=!1,n.document&&(e.exports.iframeEnabled=("function"==typeof n.postMessage||"object"==typeof n.postMessage)&&!i.isKonqueror())}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./browser":44,"./event":46,debug:void 0},],48:[function(t,e,n){(function(t){(function(){"use strict";var n={};["log","debug","warn"].forEach(function(e){var r;try{r=t.console&&t.console[e]&&t.console[e].apply}catch(i){}n[e]=r?function(){return t.console[e].apply(t.console,arguments)}:"log"===e?function(){}:n.log}),e.exports=n}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{},],49:[function(t,e,n){"use strict";e.exports={isObject:function(t){var e=typeof t;return"function"==e||"object"==e&&!!t},extend:function(t){if(!this.isObject(t))return t;for(var e,n,r=1,i=arguments.length;r<i;r++)for(n in e=arguments[r])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}}},{},],50:[function(t,e,n){"use strict";var r=t("crypto"),i="abcdefghijklmnopqrstuvwxyz012345";e.exports={string:function(t){for(var e=i.length,n=r.randomBytes(t),s=[],o=0;o<t;o++)s.push(i.substr(n[o]%e,1));return s.join("")},number:function(t){return Math.floor(Math.random()*t)},numberString:function(t){var e=(""+(t-1)).length;return(Array(e+1).join("0")+this.number(t)).slice(-e)}}},{crypto:43},],51:[function(t,e,n){"use strict";var r=function(){};e.exports=function(t){return{filterToEnabled:function(e,n){var i={main:[],facade:[]};return e?"string"==typeof e&&(e=[e]):e=[],t.forEach(function(t){t&&("websocket"!==t.transportName||!1!==n.websocket?e.length&&-1===e.indexOf(t.transportName)?r("not in whitelist",t.transportName):t.enabled(n)?(r("enabled",t.transportName),i.main.push(t),t.facadeTransport&&i.facade.push(t.facadeTransport)):r("disabled",t.transportName):r("disabled from server","websocket"))}),i}}}},{debug:void 0},],52:[function(t,e,n){"use strict";var r=t("url-parse"),i=function(){};e.exports={getOrigin:function(t){if(!t)return null;var e=new r(t);if("file:"===e.protocol)return null;var n=e.port;return n=n||("https:"===e.protocol?"443":"80"),e.protocol+"//"+e.hostname+":"+n},isOriginEqual:function(t,e){var n=this.getOrigin(t)===this.getOrigin(e);return i("same",t,e,n),n},isSchemeEqual:function(t,e){return t.split(":")[0]===e.split(":")[0]},addPath:function(t,e){var n=t.split("?");return n[0]+e+(n[1]?"?"+n[1]:"")},addQuery:function(t,e){return t+(-1===t.indexOf("?")?"?"+e:"&"+e)},isLoopbackAddr:function(t){return/^127\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(t)||/^\[::1\]$/.test(t)}}},{debug:void 0,"url-parse":57},],53:[function(t,e,n){e.exports="1.6.1"},{},],54:[function(t,e,n){"function"==typeof Object.create?e.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(t,e){if(e){function n(){}t.super_=e,n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}}},{},],55:[function(t,e,n){"use strict";var r=Object.prototype.hasOwnProperty;function i(t){try{return decodeURIComponent(t.replace(/\+/g," "))}catch(e){return null}}n.stringify=function(t,e){e=e||"";var n,i,s=[];for(i in"string"!=typeof e&&(e="?"),t)if(r.call(t,i)){if((n=t[i])||null!=n&&!isNaN(n)||(n=""),i=encodeURIComponent(i),n=encodeURIComponent(n),null===i||null===n)continue;s.push(i+"="+n)}return s.length?e+s.join("&"):""},n.parse=function(t){for(var e,n=/([^=?&]+)=?([^&]*)/g,r={};e=n.exec(t);){var s=i(e[1]),o=i(e[2]);null===s||null===o||s in r||(r[s]=o)}return r}},{},],56:[function(t,e,n){"use strict";e.exports=function(t,e){if(e=e.split(":")[0],!(t=+t))return!1;switch(e){case"http":case"ws":return 80!==t;case"https":case"wss":return 443!==t;case"ftp":return 21!==t;case"gopher":return 70!==t;case"file":return!1}return 0!==t}},{},],57:[function(t,e,n){(function(n){(function(){"use strict";var r=t("requires-port"),i=t("querystringify"),s=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,o=/[\n\r\t]/g,a=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,u=/:\d+$/,c=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,l=/^[a-zA-Z]:/;function h(t){return(t||"").toString().replace(s,"")}var f=[["#","hash"],["?","query"],function(t,e){return m(e.protocol)?t.replace(/\\/g,"/"):t},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1],],d={hash:1,query:1};function p(t){var e,r=("undefined"!=typeof window?window:void 0!==n?n:"undefined"!=typeof self?self:{}).location||{},i={},s=typeof(t=t||r);if("blob:"===t.protocol)i=new $(unescape(t.pathname),{});else if("string"==s)for(e in i=new $(t,{}),d)delete i[e];else if("object"==s){for(e in t)e in d||(i[e]=t[e]);void 0===i.slashes&&(i.slashes=a.test(t.href))}return i}function m(t){return"file:"===t||"ftp:"===t||"http:"===t||"https:"===t||"ws:"===t||"wss:"===t}function v(t,e){t=(t=h(t)).replace(o,""),e=e||{};var n,r=c.exec(t),i=r[1]?r[1].toLowerCase():"",s=!!r[2],a=!!r[3],u=0;return s?u=a?(n=r[2]+r[3]+r[4],r[2].length+r[3].length):(n=r[2]+r[4],r[2].length):a?(n=r[3]+r[4],u=r[3].length):n=r[4],"file:"===i?2<=u&&(n=n.slice(2)):m(i)?n=r[4]:i?s&&(n=n.slice(2)):2<=u&&m(e.protocol)&&(n=r[4]),{protocol:i,slashes:s||m(i),slashesCount:u,rest:n}}function $(t,e,n){if(t=(t=h(t)).replace(o,""),!(this instanceof $))return new $(t,e,n);var s,a,u,c,d,b,y=f.slice(),g=typeof e,x=this,_=0;for("object"!=g&&"string"!=g&&(n=e,e=null),n&&"function"!=typeof n&&(n=i.parse),s=!(a=v(t||"",e=p(e))).protocol&&!a.slashes,x.slashes=a.slashes||s&&e.slashes,x.protocol=a.protocol||e.protocol||"",t=a.rest,("file:"===a.protocol&&(2!==a.slashesCount||l.test(t))||!a.slashes&&(a.protocol||a.slashesCount<2||!m(x.protocol)))&&(y[3]=[/(.*)/,"pathname"]);_<y.length;_++)"function"!=typeof(c=y[_])?(u=c[0],b=c[1],u!=u?x[b]=t:"string"==typeof u?~(d="@"===u?t.lastIndexOf(u):t.indexOf(u))&&(t="number"==typeof c[2]?(x[b]=t.slice(0,d),t.slice(d+c[2])):(x[b]=t.slice(d),t.slice(0,d))):(d=u.exec(t))&&(x[b]=d[1],t=t.slice(0,d.index)),x[b]=x[b]||s&&c[3]&&e[b]||"",c[4]&&(x[b]=x[b].toLowerCase())):t=c(t,x);n&&(x.query=n(x.query)),s&&e.slashes&&"/"!==x.pathname.charAt(0)&&(""!==x.pathname||""!==e.pathname)&&(x.pathname=function(t,e){if(""===t)return e;for(var n=(e||"/").split("/").slice(0,-1).concat(t.split("/")),r=n.length,i=n[r-1],s=!1,o=0;r--;)"."===n[r]?n.splice(r,1):".."===n[r]?(n.splice(r,1),o++):o&&(0===r&&(s=!0),n.splice(r,1),o--);return s&&n.unshift(""),"."!==i&&".."!==i||n.push(""),n.join("/")}(x.pathname,e.pathname)),"/"!==x.pathname.charAt(0)&&m(x.protocol)&&(x.pathname="/"+x.pathname),r(x.port,x.protocol)||(x.host=x.hostname,x.port=""),x.username=x.password="",x.auth&&(~(d=x.auth.indexOf(":"))?(x.username=x.auth.slice(0,d),x.username=encodeURIComponent(decodeURIComponent(x.username)),x.password=x.auth.slice(d+1),x.password=encodeURIComponent(decodeURIComponent(x.password))):x.username=encodeURIComponent(decodeURIComponent(x.auth)),x.auth=x.password?x.username+":"+x.password:x.username),x.origin="file:"!==x.protocol&&m(x.protocol)&&x.host?x.protocol+"//"+x.host:"null",x.href=x.toString()}$.prototype={set:function(t,e,n){var s=this;switch(t){case"query":"string"==typeof e&&e.length&&(e=(n||i.parse)(e)),s[t]=e;break;case"port":s[t]=e,r(e,s.protocol)?e&&(s.host=s.hostname+":"+e):(s.host=s.hostname,s[t]="");break;case"hostname":s[t]=e,s.port&&(e+=":"+s.port),s.host=e;break;case"host":s[t]=e,u.test(e)?(e=e.split(":"),s.port=e.pop(),s.hostname=e.join(":")):(s.hostname=e,s.port="");break;case"protocol":s.protocol=e.toLowerCase(),s.slashes=!n;break;case"pathname":case"hash":if(e){var o="pathname"===t?"/":"#";s[t]=e.charAt(0)!==o?o+e:e}else s[t]=e;break;case"username":case"password":s[t]=encodeURIComponent(e);break;case"auth":var a=e.indexOf(":");~a?(s.username=e.slice(0,a),s.username=encodeURIComponent(decodeURIComponent(s.username)),s.password=e.slice(a+1),s.password=encodeURIComponent(decodeURIComponent(s.password))):s.username=encodeURIComponent(decodeURIComponent(e))}for(var c=0;c<f.length;c++){var l=f[c];l[4]&&(s[l[1]]=s[l[1]].toLowerCase())}return s.auth=s.password?s.username+":"+s.password:s.username,s.origin="file:"!==s.protocol&&m(s.protocol)&&s.host?s.protocol+"//"+s.host:"null",s.href=s.toString(),s},toString:function(t){t&&"function"==typeof t||(t=i.stringify);var e,n=this.host,r=this.protocol;r&&":"!==r.charAt(r.length-1)&&(r+=":");var s=r+(this.protocol&&this.slashes||m(this.protocol)?"//":"");return this.username?(s+=this.username,this.password&&(s+=":"+this.password),s+="@"):this.password?(s+=":"+this.password,s+="@"):"file:"!==this.protocol&&m(this.protocol)&&!n&&"/"!==this.pathname&&(s+="@"),(":"===n[n.length-1]||u.test(this.hostname)&&!this.port)&&(n+=":"),s+=n+this.pathname,(e="object"==typeof this.query?t(this.query):this.query)&&(s+="?"!==e.charAt(0)?"?"+e:e),this.hash&&(s+=this.hash),s}},$.extractProtocol=v,$.location=p,$.trimLeft=h,$.qs=i,e.exports=$}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{querystringify:55,"requires-port":56},]},{},[1])(1)});
