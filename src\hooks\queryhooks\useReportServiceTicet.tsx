import { QueryKeys } from "@/enums/queryKeyEnums";
import { Visit } from "@/services/visit-controller/vistiController";
import { SearchParmasType } from "@/types";
import { useQuery } from "@tanstack/react-query";

const useReportServicesTicket = ({
  startDate,
  endDate,
  enabled,
}: {
  startDate: SearchParmasType;
  endDate: SearchParmasType;
  enabled: boolean;
}) => {
  return useQuery({
    queryKey: [QueryKeys.REPORT_SERVICES_STATISTIC_TICKET],
    queryFn: () => Visit.getTicketReportServices(startDate, endDate),
    enabled,
  });
};
export default useReportServicesTicket;
