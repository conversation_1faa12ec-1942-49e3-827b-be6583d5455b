import React, { ChangeEvent, useEffect, useState } from "react";
import "./CustomNumberInput.scss";
import { TContentVisitCategory } from "@/types";
import { useVisitorStore } from "@/store/visitorsStore";
import { useTriggerStore } from "@/store/triggerStore";
import { MinusCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { Button } from "antd";

const CustomNumberInput: React.FC<Readonly<TContentVisitCategory>> = (props) => {
  const { setVisitors } = useVisitorStore();
  const [count, setCount] = useState<number>(0);
  const [countStr, setCountStr] = useState<string>("0");
  const { trigger } = useTriggerStore();

  useEffect(() => {
    setCount(0);
    setCountStr("0");
  }, [trigger]);

  const updateVisitors = (numericValue: number) => {
    if (props.pricePerPerson !== null) {
      setVisitors({
        visitCategory: Number(props.id),
        numberOfVisitors: numericValue,
        sum: props.pricePerPerson * numericValue,
      });
    } else {
      const { visitCategoryPriceConditions } = props;
      if (numericValue <= visitCategoryPriceConditions[0].to) {
        setVisitors({
          visitCategory: Number(props.id),
          numberOfVisitors: numericValue,
          sum: numericValue > 0 ? visitCategoryPriceConditions[0].price : 0,
        });
      } else {
        setVisitors({
          visitCategory: Number(props.id),
          numberOfVisitors: numericValue,
          sum: visitCategoryPriceConditions[1].price,
        });
      }
    }
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    if (!/^\d*$/.test(value)) return;
    value = value.replace(/^0+(\d)/, "$1");

    const numericValue = value === "" ? 0 : Number(value);

    setCount(numericValue);
    setCountStr(value === "" ? "0" : value);
    updateVisitors(numericValue);
  };

  const handlePlus = (e: React.MouseEvent) => {
    e.preventDefault();
    const newValue = count + 1;
    setCount(newValue);
    setCountStr(newValue.toString());
    updateVisitors(newValue);
  };

  const handleMinus = (e: React.MouseEvent) => {
    e.preventDefault();
    if (count > 0) {
      const newValue = count - 1;
      setCount(newValue);
      setCountStr(newValue.toString());
      updateVisitors(newValue);
    }
  };

  return (
    <div className='input-wrapper'>
      <Button
        shape='circle'
        className='action-buttons'
        icon={<MinusCircleOutlined style={{ color: "maroon" }} onClick={handleMinus} />}
      ></Button>
      <input
        value={countStr}
        onChange={handleChange}
        type='number'
        className='custom-input'
        min={0}
        placeholder='0'
      />
      <Button
        className='action-buttons'
        icon={<PlusCircleOutlined style={{ color: "#0b3221" }} onClick={handlePlus} />}
        shape='circle'
      ></Button>
    </div>
  );
};

export default CustomNumberInput;
